# Email Queue Setup Guide with Upstash Redis + BullMQ

This guide explains how to set up and use the robust email queue system using Upstash Redis and BullMQ for reliable email processing.

## Architecture Overview

The email queue system consists of:

1. **Email Queue Package** (`@repo/email-queue`) - Core queue functionality
2. **Email Worker Service** (`apps/email-worker`) - Standalone worker process
3. **Queue Management API** (`apps/api/app/api/email-queue/`) - REST API for queue operations
4. **Admin Interface** (`apps/app/.../email-queue/`) - Web UI for monitoring

## Prerequisites

### 1. Upstash Redis Setup

1. **Create Upstash Redis Database:**
   - Go to [Upstash Console](https://console.upstash.com/)
   - Create a new Redis database
   - Copy the REST URL and token

2. **Environment Variables:**
   ```env
   # Required for queue operations
   UPSTASH_REDIS_REST_URL=https://your-redis-instance.upstash.io
   UPSTASH_REDIS_REST_TOKEN=your_redis_token_here
   
   # Optional: Direct Redis URL for better BullMQ performance
   REDIS_URL=redis://your-redis-host:6379
   
   # SendGrid (required for email sending)
   SENDGRID_API_KEY=SG.your_sendgrid_api_key_here
   SENDGRID_FROM=<EMAIL>
   ```

### 2. Install Dependencies

```bash
# Install all workspace dependencies
pnpm install

# The following packages are automatically included:
# - @repo/email-queue (queue management)
# - bullmq (queue processing)
# - @upstash/redis (Redis client)
# - ioredis (Redis connection for BullMQ)
```

## Quick Start

### 1. Start the Email Worker

```bash
# Development mode
cd apps/email-worker
pnpm dev

# Production mode
cd apps/email-worker
pnpm start
```

### 2. Start the API Server

```bash
cd apps/api
pnpm dev
```

### 3. Start the Admin Interface

```bash
cd apps/app
pnpm dev
```

### 4. Access the Admin Interface

Navigate to: `http://localhost:3001/email-queue`

## Usage

### Queueing Emails

The system automatically queues emails when:

1. **Return/Exchange Submitted** - Sends confirmation emails
2. **Admin Status Changes** - Sends approval/rejection emails
3. **Exchange Processing** - Sends shipping/completion emails

### Manual Email Queueing

```bash
# Queue an email via API
curl -X POST http://localhost:3002/api/email-queue/add \
  -H "Content-Type: application/json" \
  -d '{
    "type": "return-request-auto-approved",
    "to": "<EMAIL>",
    "returnNumber": "RET-12345",
    "refundDays": "5-7",
    "priority": 8
  }'
```

### Queue Management

```bash
# Get queue statistics
curl http://localhost:3002/api/email-queue/stats

# Pause the queue
curl -X POST http://localhost:3002/api/email-queue/manage \
  -H "Content-Type: application/json" \
  -d '{"action": "pause"}'

# Resume the queue
curl -X POST http://localhost:3002/api/email-queue/manage \
  -H "Content-Type: application/json" \
  -d '{"action": "resume"}'

# Clean old jobs
curl -X POST http://localhost:3002/api/email-queue/manage \
  -H "Content-Type: application/json" \
  -d '{"action": "clean"}'

# Retry a failed job
curl -X POST http://localhost:3002/api/email-queue/manage \
  -H "Content-Type: application/json" \
  -d '{"action": "retry", "jobId": "job-id-here"}'
```

## Email Types and Priorities

| Email Type | Priority | Description |
|------------|----------|-------------|
| `return-request-auto-approved` | 8 | High priority for auto-approved returns |
| `return-request-manual-received` | 7 | High priority for acknowledgments |
| `return-request-manual-approved` | 8 | High priority for manual approvals |
| `exchange-request-auto-approved` | 8 | High priority for auto-approved exchanges |
| `exchange-item-shipped` | 9 | Very high priority for shipping notifications |
| `exchange-item-received` | 6 | Medium priority for completion |
| `exchange-request-manual-received` | 7 | High priority for acknowledgments |
| `exchange-request-manual-approved` | 8 | High priority for approvals |
| `request-declined` | 9 | Very high priority for declined notifications |

## Configuration

### Worker Configuration

```env
# Worker settings (optional)
EMAIL_WORKER_CONCURRENCY=5        # Number of concurrent jobs
HEALTH_CHECK_INTERVAL=30000       # Health check interval (ms)
LOG_LEVEL=info                    # Logging level
```

### Queue Configuration

- **Retry Logic:** 3 attempts with exponential backoff
- **Job Retention:** 100 completed jobs, 50 failed jobs
- **Cleanup:** Automatic cleanup of old jobs
- **Monitoring:** Real-time statistics and health checks

## Monitoring and Troubleshooting

### Admin Interface Features

1. **Real-time Statistics:**
   - Waiting, active, completed, failed job counts
   - Redis health status
   - Total jobs processed

2. **Queue Controls:**
   - Pause/resume queue processing
   - Clean old jobs
   - Retry failed jobs

3. **Job Monitoring:**
   - View recent jobs and their status
   - See failure reasons and retry counts
   - Monitor processing times

### Health Checks

The system includes comprehensive health monitoring:

- **Redis Connection:** Automatic health checks every 30 seconds
- **Worker Status:** Process monitoring and graceful shutdown
- **Queue Metrics:** Real-time job statistics
- **Error Tracking:** Detailed error logging and reporting

### Common Issues

1. **Redis Connection Failed:**
   - Check `UPSTASH_REDIS_REST_URL` and `UPSTASH_REDIS_REST_TOKEN`
   - Verify Upstash Redis database is active
   - Consider using `REDIS_URL` for direct connection

2. **Jobs Not Processing:**
   - Ensure email worker is running
   - Check worker logs for errors
   - Verify SendGrid configuration

3. **High Failure Rate:**
   - Check SendGrid API key and limits
   - Review email template data
   - Monitor retry attempts and backoff

### Logs to Monitor

```bash
# Worker logs
tail -f apps/email-worker/logs/worker.log

# API logs
tail -f apps/api/logs/api.log

# Key log messages:
# - "Email job added to queue" - Job queued successfully
# - "Email job completed successfully" - Job processed
# - "Email job failed" - Job failed with error
# - "Email worker is ready" - Worker started
# - "Redis health check passed" - System healthy
```

## Production Deployment

### 1. Environment Setup

```env
# Production Redis (recommended: dedicated instance)
REDIS_URL=redis://production-redis:6379

# Production SendGrid
SENDGRID_API_KEY=SG.production_key_here
SENDGRID_FROM=<EMAIL>

# Worker scaling
EMAIL_WORKER_CONCURRENCY=10
```

### 2. Process Management

Use a process manager like PM2 for production:

```bash
# Install PM2
npm install -g pm2

# Start email worker
pm2 start apps/email-worker/src/index.ts --name email-worker

# Monitor processes
pm2 status
pm2 logs email-worker
```

### 3. Scaling

- **Horizontal Scaling:** Run multiple worker instances
- **Vertical Scaling:** Increase worker concurrency
- **Redis Scaling:** Use Redis cluster for high throughput
- **Monitoring:** Set up alerts for queue depth and failure rates

## Benefits of Queue System

1. **Reliability:** Automatic retries and error handling
2. **Scalability:** Process emails asynchronously
3. **Monitoring:** Real-time visibility into email processing
4. **Performance:** Non-blocking email operations
5. **Resilience:** Graceful handling of service outages
6. **Priority:** Important emails processed first
7. **Debugging:** Detailed logs and job tracking
