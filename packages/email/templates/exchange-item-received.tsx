import {
  Body,
  Container,
  Head,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
// biome-ignore lint/correctness/noUnusedImports: <explanation>
import React from 'react';

type ExchangeItemReceivedEmailProps = {
  readonly returnNumber: string;
};

export const ExchangeItemReceivedEmail = ({
  returnNumber,
}: ExchangeItemReceivedEmailProps) => (
  <Tailwind>
    <Html>
      <Head />
      <Preview>Exchange #{returnNumber} is now complete</Preview>
      <Body className="bg-zinc-50 font-sans">
        <Container className="mx-auto py-12">
          <Img
            className="mx-auto"
            src="https://cdn.sanity.io/images/lpmcxine/jp_20250413/2805425d31d67c0c7268047a29b64203326c9a8f-272x68.png"
          />
          <Section className="mt-4 rounded-md bg-zinc-200 p-px">
            <Section className="rounded-[5px] bg-white p-8">
              <Text className="mt-0 mb-4 font-semibold text-2xl text-zinc-950">
                Exchange Complete
              </Text>
              <Text className="m-0 mb-4 text-zinc-700">
                We've received your returned item for exchange #{returnNumber}.
              </Text>
              <Hr className="my-4" />
              <Text className="m-0 text-zinc-700">
                Your exchange is now complete. Thank you!
              </Text>
            </Section>
          </Section>
        </Container>
      </Body>
    </Html>
  </Tailwind>
);

const ExampleExchangeItemReceivedEmail = () => (
  <ExchangeItemReceivedEmail returnNumber="EXC-12345" />
);

export default ExampleExchangeItemReceivedEmail;
