import {
  Body,
  Container,
  Head,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Tailwind,
  Text,
} from '@react-email/components';
// biome-ignore lint/correctness/noUnusedImports: <explanation>
import React from 'react';

type RequestDeclinedEmailProps = {
  readonly returnNumber: string;
};

export const RequestDeclinedEmail = ({
  returnNumber,
}: RequestDeclinedEmailProps) => (
  <Tailwind>
    <Html>
      <Head />
      <Preview>Your request #{returnNumber} has been declined</Preview>
      <Body className="bg-zinc-50 font-sans">
        <Container className="mx-auto py-12">
          <Img
            className="mx-auto"
            src="https://cdn.sanity.io/images/lpmcxine/jp_20250413/2805425d31d67c0c7268047a29b64203326c9a8f-272x68.png"
          />
          <Section className="mt-8 rounded-md bg-zinc-200 p-px">
            <Section className="rounded-[5px] bg-white p-8">
              <Text className="mt-0 mb-4 font-semibold text-2xl text-zinc-950">
                Request Declined
              </Text>
              <Text className="m-0 mb-4 text-zinc-700">
                Your request #{returnNumber} has been declined.
              </Text>
              <Hr className="my-4" />
              <Text className="m-0 mb-4 text-zinc-700">
                Your request has been declined. Please note that we cannot
                accept returns or exchanges in the following cases:
              </Text>
              <Text className="m-0 mb-2 text-zinc-700">
                • Returns due to customer reasons (size doesn't fit, different
                from expectations, wrong order, etc.)
              </Text>
              <Text className="m-0 mb-2 text-zinc-700">
                • Products that have been more than 7 days since arrival, or
                products that have been used once (please return in unopened
                condition)
              </Text>
              <Text className="m-0 mb-2 text-zinc-700">
                • Products that have become soiled, scratched, or damaged while
                in the customer's possession (including damage or scratches from
                dropping)
              </Text>
              <Text className="m-0 mb-2 text-zinc-700">
                • Items missing tags, accessories, boxes, or other accompanying
                items
              </Text>
              <Text className="m-0 mb-4 text-zinc-700">
                • Cases where we cannot confirm the customer's purchase
              </Text>
              <Hr className="my-4" />
              <Text className="m-0 text-zinc-700">
                Sorry for any inconvenience.
              </Text>
            </Section>
          </Section>
        </Container>
      </Body>
    </Html>
  </Tailwind>
);

const ExampleRequestDeclinedEmail = () => (
  <RequestDeclinedEmail returnNumber="RET-12345" />
);

export default ExampleRequestDeclinedEmail;
