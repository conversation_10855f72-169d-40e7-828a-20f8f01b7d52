import type { EmailAttachment } from '../send';

export interface ReturnLabelData {
  orderName: string;
  returnNumber: string;
  customerName?: string;
  customerAddress?: string;
  companyName?: string;
  companyAddress?: string;
  returnDepartment?: string;
}

/**
 * Generates a return label PDF and returns it as a base64 encoded attachment
 * This is a server-side version of the ReturnLabelGenerator component
 */
export async function generateReturnLabelPDF(
  data: ReturnLabelData
): Promise<EmailAttachment> {
  try {
    // Import dynamically to avoid issues in different environments
    const { jsPDF } = await import('jspdf');
    const JsBarcode = (await import('jsbarcode')).default;

    // Create a new PDF document
    const doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4',
    });

    // Set up the document
    doc.setFontSize(16);
    doc.text('Return Label', 105, 20, { align: 'center' });

    // Add order information
    doc.setFontSize(12);
    doc.text(`Order: ${data.orderName}`, 20, 40);

    if (data.customerName) {
      doc.text(`Customer: ${data.customerName}`, 20, 50);
    }

    if (data.customerAddress) {
      doc.text(`Address: ${data.customerAddress}`, 20, 60);
    }

    // Generate barcode using a virtual canvas
    // Create a virtual canvas for barcode generation
    const canvas = createVirtualCanvas();

    // Generate barcode on virtual canvas
    JsBarcode(canvas, data.returnNumber, {
      format: 'CODE128',
      width: 2,
      height: 50,
      displayValue: true,
    });

    // Add the barcode to the PDF
    const barcodeDataUrl = canvas.toDataURL('image/png');
    doc.addImage(barcodeDataUrl, 'PNG', 50, 80, 100, 30);

    // Add company address and return instructions
    doc.setFontSize(10);
    doc.text('Return to:', 20, 130);
    doc.text(data.companyName || 'Casefinite JP', 20, 140);
    if (data.returnDepartment) {
      doc.text(data.returnDepartment, 20, 145);
    }
    doc.text(data.companyAddress || 'Casefinite Address', 20, 150);

    // Add instructions
    doc.setFontSize(11);
    doc.text('Instructions:', 20, 170);
    doc.text('1. Cut along the dotted line', 25, 180);
    doc.text('2. Attach this label to your package', 25, 185);
    doc.text('3. Drop off at your nearest postal service location', 25, 190);

    // Get PDF as base64 string
    const pdfBase64 = doc.output('datauristring').split(',')[1];

    return {
      content: pdfBase64,
      filename: `return-label-${data.orderName.replace('#', '')}.pdf`,
      type: 'application/pdf',
      disposition: 'attachment',
    };
  } catch (error) {
    console.error('Error generating return label PDF:', error);
    throw new Error('Failed to generate return label PDF');
  }
}

/**
 * Creates a virtual canvas for server-side barcode generation
 * This works in Node.js environments without a DOM
 */
function createVirtualCanvas(): HTMLCanvasElement {
  // Check if we're in a browser environment
  if (typeof window !== 'undefined' && window.document) {
    return document.createElement('canvas');
  }

  // For server-side, we need to use a canvas implementation
  // This requires the 'canvas' package to be installed
  try {
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const { createCanvas } = require('canvas');
    return createCanvas(400, 100) as HTMLCanvasElement;
  } catch {
    // Fallback: create a mock canvas object that jsbarcode can work with
    const mockCanvas = {
      width: 400,
      height: 100,
      getContext: () => ({
        fillRect: () => {
          // Mock implementation
        },
        fillText: () => {
          // Mock implementation
        },
        measureText: () => ({ width: 0 }),
        font: '',
        fillStyle: '',
        textAlign: '',
        textBaseline: '',
      }),
      toDataURL: () => {
        // Return a minimal base64 encoded 1x1 transparent PNG
        return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
      },
    };
    return mockCanvas as unknown as HTMLCanvasElement;
  }
}
