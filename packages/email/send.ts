import { render } from '@react-email/components';
import { log } from '@repo/observability/log';
import type { ReactElement } from 'react';
import { sendgrid } from './index';
import { keys } from './keys';

const env = keys();

export interface EmailAttachment {
  content: string; // base64 encoded content
  filename: string;
  type: string; // MIME type
  disposition: 'attachment' | 'inline';
}

export interface SendEmailOptions {
  to: string | string[];
  subject: string;
  template: ReactElement;
  from?: string;
  attachments?: EmailAttachment[];
}

export async function sendEmail({
  to,
  subject,
  template,
  from = env.SENDGRID_FROM,
  attachments,
}: SendEmailOptions) {
  try {
    const html = await render(template);

    const msg = {
      to: Array.isArray(to) ? to : [to],
      from,
      subject,
      html,
      ...(attachments && attachments.length > 0 && { attachments }),
    };

    const result = await sendgrid.send(msg);
    return { success: true, result };
  } catch (error) {
    console.error('Failed to send email:', error);
    return { success: false, error };
  }
}

// Specific email sending functions for each template type
export async function sendReturnRequestAutoApprovedEmail(
  to: string,
  returnNumber: string,
  refundDays?: string,
  returnLabelData?: {
    returnLabelOption?: string;
    orderName: string;
    customerName?: string;
    customerAddress?: string;
    companyName?: string;
    companyAddress?: string;
    returnDepartment?: string;
  }
) {
  const { ReturnRequestAutoApprovedEmail } = await import(
    './templates/return-request-auto-approved'
  );

  let attachments: EmailAttachment[] | undefined;

  // Generate PDF attachment if return label option is 'print'
  if (returnLabelData?.returnLabelOption === 'print') {
    try {
      const { generateReturnLabelPDF } = await import('./utils/pdf-generator');
      const pdfAttachment = await generateReturnLabelPDF({
        orderName: returnLabelData.orderName,
        returnNumber,
        customerName: returnLabelData.customerName,
        customerAddress: returnLabelData.customerAddress,
        companyName: returnLabelData.companyName,
        companyAddress: returnLabelData.companyAddress,
        returnDepartment: returnLabelData.returnDepartment,
      });
      attachments = [pdfAttachment];
    } catch (error) {
      console.error('Failed to generate return label PDF:', error);
      // Continue without attachment rather than failing the email
    }
  }

  return sendEmail({
    to,
    subject: `Return Request #${returnNumber} Approved`,
    template: ReturnRequestAutoApprovedEmail({ returnNumber, refundDays }),
    attachments,
  });
}

export async function sendReturnRequestManualReceivedEmail(
  to: string,
  returnNumber: string,
  reviewDays?: string
) {
  const { ReturnRequestManualReceivedEmail } = await import(
    './templates/return-request-manual-received'
  );

  return sendEmail({
    to,
    subject: `Return Request #${returnNumber} Received`,
    template: ReturnRequestManualReceivedEmail({ returnNumber, reviewDays }),
  });
}

export async function sendReturnRequestManualApprovedEmail(
  to: string,
  returnNumber: string,
  refundDays?: string,
  returnInstructions?: string,
  returnLabelData?: {
    returnLabelOption?: string;
    orderName: string;
    customerName?: string;
    customerAddress?: string;
    companyName?: string;
    companyAddress?: string;
    returnDepartment?: string;
  }
) {
  const { ReturnRequestManualApprovedEmail } = await import(
    './templates/return-request-manual-approved'
  );

  let attachments: EmailAttachment[] | undefined;

  // Generate PDF attachment if return label option is 'print'
  if (returnLabelData?.returnLabelOption === 'print') {
    try {
      const { generateReturnLabelPDF } = await import('./utils/pdf-generator');
      const pdfAttachment = await generateReturnLabelPDF({
        orderName: returnLabelData.orderName,
        returnNumber,
        customerName: returnLabelData.customerName,
        customerAddress: returnLabelData.customerAddress,
        companyName: returnLabelData.companyName,
        companyAddress: returnLabelData.companyAddress,
        returnDepartment: returnLabelData.returnDepartment,
      });
      attachments = [pdfAttachment];
    } catch (error) {
      console.error('Failed to generate return label PDF:', error);
      // Continue without attachment rather than failing the email
    }
  }

  return sendEmail({
    to,
    subject: `Return Request #${returnNumber} Approved`,
    template: ReturnRequestManualApprovedEmail({
      returnNumber,
      refundDays,
      returnInstructions,
    }),
    attachments,
  });
}

export async function sendExchangeRequestAutoApprovedEmail(
  to: string,
  returnNumber: string,
  returnLabelData?: {
    returnLabelOption?: string;
    orderName: string;
    customerName?: string;
    customerAddress?: string;
    companyName?: string;
    companyAddress?: string;
    returnDepartment?: string;
  }
) {
  const { ExchangeRequestAutoApprovedEmail } = await import(
    './templates/exchange-request-auto-approved'
  );

  let attachments: EmailAttachment[] | undefined;

  // Generate PDF attachment if return label option is 'print'
  if (returnLabelData?.returnLabelOption === 'print') {
    try {
      const { generateReturnLabelPDF } = await import('./utils/pdf-generator');
      const pdfAttachment = await generateReturnLabelPDF({
        orderName: returnLabelData.orderName,
        returnNumber,
        customerName: returnLabelData.customerName,
        customerAddress: returnLabelData.customerAddress,
        companyName: returnLabelData.companyName,
        companyAddress: returnLabelData.companyAddress,
        returnDepartment: returnLabelData.returnDepartment,
      });
      attachments = [pdfAttachment];
    } catch (error) {
      console.error('Failed to generate return label PDF:', error);
      // Continue without attachment rather than failing the email
    }
  }

  return sendEmail({
    to,
    subject: `Exchange Request #${returnNumber} Approved`,
    template: ExchangeRequestAutoApprovedEmail({ returnNumber }),
    attachments,
  });
}

export async function sendExchangeItemShippedEmail(
  to: string,
  returnNumber: string,
  trackingNumber: string
) {
  const { ExchangeItemShippedEmail } = await import(
    './templates/exchange-item-shipped'
  );

  return sendEmail({
    to,
    subject: `Replacement Item Shipped - Request #${returnNumber}`,
    template: ExchangeItemShippedEmail({ returnNumber, trackingNumber }),
  });
}

export async function sendExchangeItemReceivedEmail(
  to: string,
  returnNumber: string
) {
  const { ExchangeItemReceivedEmail } = await import(
    './templates/exchange-item-received'
  );

  return sendEmail({
    to,
    subject: `Exchange Complete - Request #${returnNumber}`,
    template: ExchangeItemReceivedEmail({ returnNumber }),
  });
}

export async function sendExchangeRequestManualReceivedEmail(
  to: string,
  returnNumber: string
) {
  const { ExchangeRequestManualReceivedEmail } = await import(
    './templates/exchange-request-manual-received'
  );

  return sendEmail({
    to,
    subject: `Exchange Request #${returnNumber} Received`,
    template: ExchangeRequestManualReceivedEmail({ returnNumber }),
  });
}

export async function sendExchangeRequestManualApprovedEmail(
  to: string,
  returnNumber: string,
  returnLabelData?: {
    returnLabelOption?: string;
    orderName: string;
    customerName?: string;
    customerAddress?: string;
    companyName?: string;
    companyAddress?: string;
    returnDepartment?: string;
  }
) {
  const { ExchangeRequestManualApprovedEmail } = await import(
    './templates/exchange-request-manual-approved'
  );

  let attachments: EmailAttachment[] | undefined;

  // Generate PDF attachment if return label option is 'print'
  if (returnLabelData?.returnLabelOption === 'print') {
    try {
      const { generateReturnLabelPDF } = await import('./utils/pdf-generator');
      const pdfAttachment = await generateReturnLabelPDF({
        orderName: returnLabelData.orderName,
        returnNumber,
        customerName: returnLabelData.customerName,
        customerAddress: returnLabelData.customerAddress,
        companyName: returnLabelData.companyName,
        companyAddress: returnLabelData.companyAddress,
        returnDepartment: returnLabelData.returnDepartment,
      });
      attachments = [pdfAttachment];
    } catch (error) {
      console.error('Failed to generate return label PDF:', error);
      // Continue without attachment rather than failing the email
    }
  }

  return sendEmail({
    to,
    subject: `Exchange Request #${returnNumber} Approved`,
    template: ExchangeRequestManualApprovedEmail({ returnNumber }),
    attachments,
  });
}

export async function sendRequestDeclinedEmail(
  to: string,
  returnNumber: string
) {
  const { RequestDeclinedEmail } = await import('./templates/request-declined');

  return sendEmail({
    to,
    subject: `Request #${returnNumber} Declined`,
    template: RequestDeclinedEmail({ returnNumber }),
  });
}
