import { database } from '@repo/database';
import type { User } from '@repo/database/types';
import { log } from '@repo/observability/log';
import { type BetterAuthOptions, betterAuth } from 'better-auth';
import { prismaAdapter } from 'better-auth/adapters/prisma';
import { nextCookies } from 'better-auth/next-js';
import { admin, bearer, jwt, magicLink } from 'better-auth/plugins';
import { keys } from './keys';
import { ac, superAdminRole } from './permissions';

const options = {
  user: {
    additionalFields: {
      firstName: {
        type: 'string',
        required: false,
        defaultValue: null,
        input: true,
      },
      lastName: {
        type: 'string',
        required: false,
        defaultValue: null,
        input: true,
      },
      phone: {
        type: 'string',
        required: false,
        defaultValue: null,
        input: true,
      },
      dob: {
        type: 'date',
        required: false,
        defaultValue: null,
        input: true,
      },
    },
  },
  emailAndPassword: {
    enabled: true,
    autoSignIn: true,
  },
  database: prismaAdapter(database, {
    provider: 'postgresql',
  }),
  session: {
    cookieCache: {
      enabled: true,
    },
  },
  plugins: [
    magicLink({
      // biome-ignore lint/suspicious/useAwait: <explanation>
      sendMagicLink: async ({ email, token, url }, _request) => {
        log.info('sending magic link to', { email, token, url });
        // send email to user
        // use sendgrid to send email
      },
    }),
    jwt(),
    bearer(),
    admin({
      defaultRole: 'customer',
      ac,
      roles: {
        superAdmin: superAdminRole,
      },
      adminRoles: ['super-admin', 'admin'],
    }),

    nextCookies(),
  ],
  databaseHooks: {
    user: {
      create: {
        after: async (context) => {
          const user = context as User;

          try {
            const response = await fetch(
              `${keys().NEXT_PUBLIC_CORE_BACKEND_URL}/webhooks/v1/license`,
              {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'X-Webhook-Secret': 'your-webhook-secret-here',
                },
                body: JSON.stringify({
                  event: 'user.created',
                  data: {
                    userId: user.id,
                    email: user.email,
                    name: user.name,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    phone: user.phone,
                    dob: user.dob,
                  },
                }),
              }
            );

            const data = await response.json();

            log.info('User synced with core:', { data });
          } catch (error) {
            log.error('Failed to sync user with core:', { error });
          }
        },
      },
    },
  },
  onAPIError: {
    throw: true,
    onError: (error) => {
      log.error('Auth error:', { error });
    },
    errorURL: '/auth/error',
  },
} satisfies BetterAuthOptions;

export const auth: ReturnType<typeof betterAuth<typeof options>> =
  betterAuth(options);
