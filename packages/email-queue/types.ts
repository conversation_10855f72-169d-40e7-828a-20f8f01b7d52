import { z } from 'zod';

// Base email job data schema
export const emailJobDataSchema = z.object({
  to: z.string().email(),
  returnNumber: z.string().min(1),
  type: z.enum([
    'return-request-auto-approved',
    'return-request-manual-received',
    'return-request-manual-approved',
    'exchange-request-auto-approved',
    'exchange-item-shipped',
    'exchange-item-received',
    'exchange-request-manual-received',
    'exchange-request-manual-approved',
    'request-declined',
  ]),
  // Optional fields based on email type
  refundDays: z.string().optional(),
  reviewDays: z.string().optional(),
  returnInstructions: z.string().optional(),
  trackingNumber: z.string().optional(),
  // Metadata
  priority: z.number().min(1).max(10).default(5),
  delay: z.number().min(0).default(0), // Delay in milliseconds
  attempts: z.number().min(1).max(10).default(3),
});

export type EmailJobData = z.infer<typeof emailJobDataSchema>;

// Job options for BullMQ
export interface EmailJobOptions {
  priority?: number;
  delay?: number;
  attempts?: number;
  backoff?: {
    type: 'exponential' | 'fixed';
    delay: number;
  };
  removeOnComplete?: number;
  removeOnFail?: number;
}

// Email job result
export interface EmailJobResult {
  success: boolean;
  messageId?: string;
  error?: string;
  sentAt: string;
  attempts: number;
}

// Queue events
export interface EmailQueueEvents {
  'job:completed': { jobId: string; result: EmailJobResult };
  'job:failed': { jobId: string; error: string; attempts: number };
  'job:retry': { jobId: string; attempts: number };
  'queue:health': {
    waiting: number;
    active: number;
    completed: number;
    failed: number;
  };
}
