import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const keys = () =>
  createEnv({
    server: {
      UPSTASH_REDIS_REST_URL: z.string().min(1).url(),
      UPSTASH_REDIS_REST_TOKEN: z.string().min(1),
      // Optional: Redis connection for BullMQ (if using direct Redis instead of REST)
      REDIS_URL: z.string().url().optional(),
    },
    runtimeEnv: {
      UPSTASH_REDIS_REST_URL: process.env.UPSTASH_REDIS_REST_URL,
      UPSTASH_REDIS_REST_TOKEN: process.env.UPSTASH_REDIS_REST_TOKEN,
      REDIS_URL: process.env.REDIS_URL,
    },
  });
