import {
  sendExchangeItemReceivedEmail,
  sendExchangeItemShippedEmail,
  sendExchangeRequestAutoApprovedEmail,
  sendExchangeRequestManualApprovedEmail,
  sendExchangeRequestManualReceivedEmail,
  sendRequestDeclinedEmail,
  sendReturnRequestAutoApprovedEmail,
  sendReturnRequestManualApprovedEmail,
  sendReturnRequestManualReceivedEmail,
} from '@repo/email';
import { log } from '@repo/observability/log';
import { type Job, Worker } from 'bullmq';
import { getRedisConnection } from './redis';
import type { EmailJobData, EmailJobResult } from './types';

// Email sending function mapping
const emailSenders = {
  'return-request-auto-approved': sendReturnRequestAutoApprovedEmail,
  'return-request-manual-received': sendReturnRequestManualReceivedEmail,
  'return-request-manual-approved': sendReturnRequestManualApprovedEmail,
  'exchange-request-auto-approved': sendExchangeRequestAutoApprovedEmail,
  'exchange-item-shipped': sendExchangeItemShippedEmail,
  'exchange-item-received': sendExchangeItemReceivedEmail,
  'exchange-request-manual-received': sendExchangeRequestManualReceivedEmail,
  'exchange-request-manual-approved': sendExchangeRequestManualApprovedEmail,
  'request-declined': sendRequestDeclinedEmail,
} as const;

// Process email job
const processEmailJob = async (
  job: Job<EmailJobData>
): Promise<EmailJobResult> => {
  const { data } = job;
  const startTime = Date.now();

  try {
    log.info('Processing email job', {
      jobId: job.id,
      emailType: data.type,
      to: data.to,
      returnNumber: data.returnNumber,
      attempt: job.attemptsMade + 1,
    });

    // Get the appropriate email sender function
    const emailSender = emailSenders[data.type];
    if (!emailSender) {
      throw new Error(`Unknown email type: ${data.type}`);
    }

    // Prepare arguments based on email type
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    let result: any;
    switch (data.type) {
      case 'return-request-auto-approved':
        result = await (
          emailSender as typeof sendReturnRequestAutoApprovedEmail
        )(data.to, data.returnNumber, data.refundDays);
        break;
      case 'return-request-manual-received':
        result = await (
          emailSender as typeof sendReturnRequestManualReceivedEmail
        )(data.to, data.returnNumber, data.reviewDays ?? '3');
        break;
      case 'return-request-manual-approved':
        result = await (
          emailSender as typeof sendReturnRequestManualApprovedEmail
        )(data.to, data.returnNumber, data.refundDays, data.returnInstructions);
        break;
      case 'exchange-item-shipped': {
        if (!data.trackingNumber) {
          throw new Error(
            'Tracking number is required for exchange-item-shipped emails'
          );
        }

        result = await (emailSender as typeof sendExchangeItemShippedEmail)(
          data.to,
          data.returnNumber,
          data.trackingNumber
        );

        break;
      }
      case 'exchange-request-auto-approved':
      case 'exchange-item-received':
      case 'exchange-request-manual-received':
      case 'exchange-request-manual-approved':
      case 'request-declined':
        result = await (emailSender as typeof sendRequestDeclinedEmail)(
          data.to,
          data.returnNumber
        );
        break;
      default:
        throw new Error(`Unhandled email type: ${data.type}`);
    }

    if (!result.success) {
      throw new Error(result.error?.toString() || 'Email sending failed');
    }

    const processingTime = Date.now() - startTime;
    const jobResult: EmailJobResult = {
      success: true,
      messageId: result.result?.messageId || 'unknown',
      sentAt: new Date().toISOString(),
      attempts: job.attemptsMade + 1,
    };

    log.info('Email job completed successfully', {
      jobId: job.id,
      emailType: data.type,
      to: data.to,
      processingTime,
      attempts: job.attemptsMade + 1,
    });

    return jobResult;
  } catch (error) {
    const processingTime = Date.now() - startTime;
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';

    log.error('Email job failed', {
      jobId: job.id,
      emailType: data.type,
      to: data.to,
      error: errorMessage,
      processingTime,
      attempts: job.attemptsMade + 1,
    });

    throw error;
  }
};

// Create and start the worker
export const createEmailWorker = (concurrency = 5) => {
  const worker = new Worker('email-queue', processEmailJob, {
    connection: getRedisConnection(),
    concurrency,
    // removeOnComplete: 100,
    // removeOnFail: 50,
  });

  // Worker event listeners
  worker.on('ready', () => {
    log.info('Email worker is ready', { concurrency });
  });

  worker.on('error', (error) => {
    log.error('Email worker error', { error });
  });

  worker.on('failed', (job, error) => {
    log.error('Email job failed in worker', {
      jobId: job?.id,
      error: error.message,
      attempts: job?.attemptsMade,
    });
  });

  worker.on('completed', (job, result) => {
    log.info('Email job completed in worker', {
      jobId: job.id,
      result,
    });
  });

  return worker;
};

// Graceful shutdown
export const shutdownWorker = async (worker: Worker) => {
  log.info('Shutting down email worker...');
  await worker.close();
  log.info('Email worker shut down successfully');
};
