// Main exports for the email queue package
export * from './types';
export * from './queue';
export * from './worker';
export * from './redis';

// Convenience functions for common operations
export {
  addEmailToQueue,
  getQueueStats,
  cleanQueue,
  pauseQueue,
  resumeQueue,
  retryJob,
  retryAllFailedJobs,
} from './queue';
export { createEmailWorker, shutdownWorker } from './worker';
export { checkRedisHealth } from './redis';

// Helper function to add different types of emails to queue
import { addEmailToQueue } from './queue';
import type { EmailJobData, EmailJobOptions } from './types';

export const queueReturnRequestAutoApprovedEmail = (
  to: string,
  returnNumber: string,
  refundDays?: string,
  options?: EmailJobOptions
) => {
  const emailData: EmailJobData = {
    type: 'return-request-auto-approved',
    to,
    returnNumber,
    refundDays,
    priority: 8, // High priority for auto-approved
    delay: 0,
    attempts: 3,
  };
  return addEmailToQueue(emailData, options);
};

export const queueReturnRequestManualReceivedEmail = (
  to: string,
  returnNumber: string,
  reviewDays?: string,
  options?: EmailJobOptions
) => {
  const emailData: EmailJobData = {
    type: 'return-request-manual-received',
    to,
    returnNumber,
    reviewDays,
    priority: 7, // High priority for acknowledgments
    delay: 0,
    attempts: 3,
  };
  return addEmailToQueue(emailData, options);
};

export const queueReturnRequestManualApprovedEmail = (
  to: string,
  returnNumber: string,
  refundDays?: string,
  returnInstructions?: string,
  options?: EmailJobOptions
) => {
  const emailData: EmailJobData = {
    type: 'return-request-manual-approved',
    to,
    returnNumber,
    refundDays,
    returnInstructions,
    priority: 8, // High priority for approvals
    delay: 0,
    attempts: 3,
  };
  return addEmailToQueue(emailData, options);
};

export const queueExchangeRequestAutoApprovedEmail = (
  to: string,
  returnNumber: string,
  options?: EmailJobOptions
) => {
  const emailData: EmailJobData = {
    type: 'exchange-request-auto-approved',
    to,
    returnNumber,
    priority: 8, // High priority for auto-approved
    delay: 0,
    attempts: 3,
  };
  return addEmailToQueue(emailData, options);
};

export const queueExchangeItemShippedEmail = (
  to: string,
  returnNumber: string,
  trackingNumber: string,
  options?: EmailJobOptions
) => {
  const emailData: EmailJobData = {
    type: 'exchange-item-shipped',
    to,
    returnNumber,
    trackingNumber,
    priority: 9, // Very high priority for shipping notifications
    delay: 0,
    attempts: 3,
  };
  return addEmailToQueue(emailData, options);
};

export const queueExchangeItemReceivedEmail = (
  to: string,
  returnNumber: string,
  options?: EmailJobOptions
) => {
  const emailData: EmailJobData = {
    type: 'exchange-item-received',
    to,
    returnNumber,
    priority: 6, // Medium priority for completion
    delay: 0,
    attempts: 3,
  };
  return addEmailToQueue(emailData, options);
};

export const queueExchangeRequestManualReceivedEmail = (
  to: string,
  returnNumber: string,
  options?: EmailJobOptions
) => {
  const emailData: EmailJobData = {
    type: 'exchange-request-manual-received',
    to,
    returnNumber,
    priority: 7, // High priority for acknowledgments
    delay: 0,
    attempts: 3,
  };
  return addEmailToQueue(emailData, options);
};

export const queueExchangeRequestManualApprovedEmail = (
  to: string,
  returnNumber: string,
  options?: EmailJobOptions
) => {
  const emailData: EmailJobData = {
    type: 'exchange-request-manual-approved',
    to,
    returnNumber,
    priority: 8, // High priority for approvals
    delay: 0,
    attempts: 3,
  };
  return addEmailToQueue(emailData, options);
};

export const queueRequestDeclinedEmail = (
  to: string,
  returnNumber: string,
  options?: EmailJobOptions
) => {
  const emailData: EmailJobData = {
    type: 'request-declined',
    to,
    returnNumber,
    priority: 9, // Very high priority for declined notifications
    delay: 0,
    attempts: 3,
  };
  return addEmailToQueue(emailData, options);
};
