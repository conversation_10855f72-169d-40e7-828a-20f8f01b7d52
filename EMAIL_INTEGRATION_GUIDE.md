# Email Integration Guide

This document explains how email notifications are automatically triggered based on return/exchange status changes and workflow events.

## Email Triggers Overview

The email system is now fully integrated into the returns workflow and automatically sends appropriate emails based on status changes and processing events.

## Trigger Points

### 1. Initial Submission (`apps/portal/app/api/submit-return/route.ts`)

When a customer submits a return or exchange request:

**Auto-Approved Requests:**
- **Return:** Sends `return-request-auto-approved` email
- **Exchange:** Sends `exchange-request-auto-approved` email

**Manual Review Requests:**
- **Return:** Sends `return-request-manual-received` email
- **Exchange:** Sends `exchange-request-manual-received` email

### 2. Admin Status Changes (`apps/app/.../return-requests/actions.ts`)

When admin updates the status through the admin panel:

**Manual Approval (pending → approved):**
- **Return:** Sends `return-request-manual-approved` email with return instructions
- **Exchange:** Sends `exchange-request-manual-approved` email

**Request Rejection (any status → rejected):**
- **Any type:** Sends `request-declined` email with detailed rejection reasons

### 3. Exchange Processing

**When tracking number is added (`processYamatoCsv`):**
- **Exchange:** Sends `exchange-item-shipped` email with tracking information
- Automatically updates `processed` status to `exchange_shipped`

**When return item is received (`updateReturnRequestStatus`):**
- **Exchange:** Sends `exchange-item-received` email when status changes from `exchange_shipped` to `completed`

## Email Types and Content

### Return Emails

1. **return-request-auto-approved**
   - Sent: Immediately upon auto-approval
   - Content: Approval confirmation, refund timeline (5-7 days), return instructions

2. **return-request-manual-received**
   - Sent: When manual review is required
   - Content: Acknowledgment, review timeline (3 business days)

3. **return-request-manual-approved**
   - Sent: When admin manually approves
   - Content: Approval confirmation, refund timeline, detailed return instructions from admin notes

### Exchange Emails

4. **exchange-request-auto-approved**
   - Sent: Immediately upon auto-approval
   - Content: Exchange approval confirmation

5. **exchange-request-manual-received**
   - Sent: When manual review is required
   - Content: Acknowledgment of exchange request

6. **exchange-request-manual-approved**
   - Sent: When admin manually approves
   - Content: Exchange approval confirmation

7. **exchange-item-shipped**
   - Sent: When tracking number is added via Yamato CSV processing
   - Content: Replacement item shipped notification with tracking number

8. **exchange-item-received**
   - Sent: When return item is scanned and processed as completed
   - Content: Exchange completion confirmation

### Declined Requests

9. **request-declined**
   - Sent: When any request is rejected
   - Content: Detailed rejection reasons and policy information

## Status Flow and Email Mapping

```
Initial Submission:
├── Auto-approved → return/exchange-request-auto-approved
└── Manual review → return/exchange-request-manual-received

Admin Actions:
├── Manual approval → return/exchange-request-manual-approved
└── Rejection → request-declined

Exchange Processing:
├── Tracking added → exchange-item-shipped
└── Return received → exchange-item-received
```

## Technical Implementation

### Email Sending
- Uses SendGrid API through the email package (`@repo/email`)
- All emails are sent via the universal endpoint: `/api/email/send`
- Includes proper error handling and logging
- Non-blocking: Email failures don't prevent status updates

### Environment Variables Required
```env
SENDGRID_API_KEY=SG.your_api_key_here
SENDGRID_FROM=<EMAIL>
NEXT_PUBLIC_API_URL=http://localhost:3002  # For API calls
```

### Error Handling
- Email failures are logged but don't prevent the main operation
- Uses structured logging with `@repo/observability/log`
- Includes retry logic through SendGrid's built-in mechanisms

## Testing

### Manual Testing
1. Submit a return/exchange request through the portal
2. Check admin panel for status changes
3. Use barcode scanner to process returns
4. Upload Yamato CSV for tracking numbers

### Email Preview
- Access email templates at: `http://localhost:3006` (email preview app)
- All templates include realistic sample data

### API Testing
```bash
# Test individual email sending
curl -X POST http://localhost:3002/api/email/return-request-auto-approved \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "returnNumber": "RET-12345",
    "refundDays": "5-7"
  }'
```

## Monitoring

### Logs to Monitor
- Email sending success/failure logs
- Status change logs
- API response logs from email service

### Key Metrics
- Email delivery success rate
- Time between status change and email sent
- Failed email attempts and reasons

## Troubleshooting

### Common Issues
1. **Emails not sending:** Check SendGrid API key and from address
2. **Wrong email content:** Verify template props and data mapping
3. **Missing emails:** Check status change logic and trigger conditions

### Debug Steps
1. Check application logs for email sending attempts
2. Verify SendGrid dashboard for delivery status
3. Test email templates in preview app
4. Validate API endpoints with direct calls

## Future Enhancements

### Potential Improvements
- Email templates in multiple languages
- Customer email preferences
- Email delivery status tracking
- Automated retry mechanisms
- Email analytics and reporting
