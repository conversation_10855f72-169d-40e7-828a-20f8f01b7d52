{"name": "email-worker", "version": "0.0.0", "private": true, "scripts": {"dev": "tsx watch --require ./src/load-env.ts src/index.ts", "start": "tsx --require ./src/load-env.ts src/index.ts", "build": "pnpm clean:dist && pnpm build:bundle", "build:bundle": "esbuild src/index.ts --bundle --platform=node --target=node18 --outfile=dist/index.js --external:fsevents --external:canvas --sourcemap --minify", "build:prod": "pnpm build && pnpm copy:package", "copy:package": "cp package-prod.json dist/package.json", "start:prod": "node dist/index.js", "clean": "git clean -xdf .cache .turbo dist node_modules", "clean:dist": "rm -rf dist", "typecheck": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "typecheck-strict": "tsc --noEmit"}, "dependencies": {"@repo/email": "workspace:*", "@repo/email-queue": "workspace:*", "@repo/observability": "workspace:*", "@t3-oss/env-nextjs": "^0.12.0", "dotenv": "^16.5.0", "zod": "^3.24.3"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "22.14.1", "esbuild": "^0.24.2", "tsx": "^4.19.2", "typescript": "^5.8.3"}}