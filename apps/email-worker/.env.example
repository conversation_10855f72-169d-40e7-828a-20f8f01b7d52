# Redis Configuration (required)
UPSTASH_REDIS_REST_URL=https://your-redis-instance.upstash.io
UPSTASH_REDIS_REST_TOKEN=your_redis_token_here

# Optional: Direct Redis URL for BullMQ (recommended for better performance)
REDIS_URL=redis://your-redis-host:6379

# SendGrid Configuration (required)
SENDGRID_API_KEY=SG.your_sendgrid_api_key_here
SENDGRID_FROM=<EMAIL>

# Worker Configuration (optional)
EMAIL_WORKER_CONCURRENCY=5
HEALTH_CHECK_INTERVAL=30000

# Logging (optional)
LOG_LEVEL=info
