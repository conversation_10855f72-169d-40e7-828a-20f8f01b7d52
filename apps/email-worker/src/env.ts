import { keys as queue } from '@repo/email-queue/keys';
import { keys as email } from '@repo/email/keys';
import { keys as observability } from '@repo/observability/keys';
// @ts-ignore - @t3-oss/env-nextjs is an ES module, but tsx handles this at runtime
import { createEnv } from '@t3-oss/env-core';
import { z } from 'zod';

export const env = createEnv({
  extends: [email(), queue(), observability()],
  server: {
    // Worker-specific configuration
    EMAIL_WORKER_CONCURRENCY: z.coerce.number().min(1).max(20).default(5),
    HEALTH_CHECK_INTERVAL: z.coerce.number().min(1000).default(30000), // 30 seconds
    LOG_LEVEL: z.enum(['debug', 'info', 'warn', 'error']).default('info'),
  },
  client: {},
  runtimeEnv: {
    EMAIL_WORKER_CONCURRENCY: process.env.EMAIL_WORKER_CONCURRENCY,
    HEALTH_CHECK_INTERVAL: process.env.HEALTH_CHECK_INTERVAL,
    LOG_LEVEL: process.env.LOG_LEVEL,
  },
});
