module.exports = {
  apps: [
    {
      name: "email-worker",
      script: "dist/index.js",
      interpreter: "node",
      interpreter_args: "--require ./src/load-env.ts",
      args: "",
      exec_mode: "fork", // Change to 'cluster' if you want multiple instances
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "1G",
      env: {
        NODE_ENV: "production",
        // Add any additional environment variables here if needed
      },
      log_date_format: "YYYY-MM-DD HH:mm:ss",
      error_file: "./logs/email-worker.err.log",
      out_file: "./logs/email-worker.out.log",
      pid_file: "./pids/email-worker.pid",
      merge_logs: true,
      cwd: "/root/senders_return_saas/apps/email-worker"
    },
  ],
};
