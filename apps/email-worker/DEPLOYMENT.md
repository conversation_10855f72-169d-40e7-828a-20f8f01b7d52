# Email Worker Production Deployment Guide

This guide will help you deploy the email worker to a VPS without using tsx, with a proper production build.

## 🏗️ Build Process

The email worker now uses **esbuild** to create a single bundled JavaScript file that includes all dependencies. This eliminates the need for tsx in production.

### What the build does:
- Bundles all TypeScript code into a single `index.js` file
- Includes all workspace dependencies (`@repo/*` packages)
- Minifies the code for production
- Creates source maps for debugging
- Generates a production `package.json`

## 🚀 Quick Deployment

1. **Build the production bundle:**
   ```bash
   pnpm build:prod
   ```

## 🔧 Server Setup

### Prerequisites on your VPS:

1. **Install Node.js (18+):**
   ```bash
   # Using NodeSource repository
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   
   # Or using nvm
   curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
   nvm install 18
   nvm use 18
   ```

2. **Install PM2 (Process Manager):**
   ```bash
   npm install -g pm2
   ```

## 🎯 Running in Production

### Start the service:
```bash
pm2 start ecosystem.config.js
```

### Useful PM2 commands:
```bash
# Check status
pm2 status

# View logs
pm2 logs email-worker

# Restart
pm2 restart email-worker

# Stop
pm2 stop email-worker

# Monitor
pm2 monit

# Save PM2 configuration
pm2 save

# Set up PM2 to start on boot
pm2 startup
```

## 🔄 Updates and Redeployment

```bash
pnpm build:prod
# Upload and extract as before
pm2 restart email-worker
```

## 🛠️ Troubleshooting

### Common Issues:

1. **"Cannot find module" errors:**
   - The bundled build should include all dependencies
   - Check that the build completed successfully

2. **Environment variable issues:**
   - Verify `.env` file exists and has correct values
   - Check PM2 is loading the environment file correctly

3. **Redis connection issues:**
   - Verify Upstash Redis credentials
   - Check network connectivity from VPS to Upstash

### Debug commands:
```bash
# Check if the bundled file runs directly
node dist/index.js

# Check PM2 process details
pm2 show email-worker

# Check system resources
pm2 monit
```

## 📊 Monitoring

The worker includes built-in health checks that run every 30 minutes (configurable). Monitor the logs to ensure:

- Redis connections are healthy
- Email jobs are being processed
- No uncaught exceptions or rejections

Set up log rotation for production:
```bash
sudo logrotate -d /etc/logrotate.d/email-worker
```

## 🔐 Security Considerations

1. **Firewall:** Only open necessary ports
2. **Environment files:** Secure your `.env` file
3. **Updates:** Keep Node.js and system packages updated
4. **Monitoring:** Set up alerts for service failures
