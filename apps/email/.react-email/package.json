{"name": "preview-server", "version": "4.0.7", "description": "A live preview of your emails right in your browser.", "bin": {"email": "./dist/cli/index.js"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/resend/react-email.git", "directory": "packages/react-email"}, "keywords": ["react", "email"], "engines": {"node": ">=18.0.0"}, "dependencies": {"@babel/parser": "7.24.5", "@babel/traverse": "7.25.6", "chalk": "4.1.2", "chokidar": "4.0.3", "commander": "11.1.0", "debounce": "2.0.0", "esbuild": "0.25.0", "glob": "10.3.4", "log-symbols": "4.1.0", "mime-types": "2.1.35", "next": "15.2.4", "normalize-path": "3.0.0", "ora": "5.4.1", "socket.io": "4.8.1"}, "devDependencies": {"@babel/core": "7.26.10", "@lottiefiles/dotlottie-react": "0.12.3", "@radix-ui/colors": "1.0.1", "@radix-ui/react-collapsible": "1.1.0", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-popover": "1.1.1", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-tabs": "1.1.1", "@radix-ui/react-toggle-group": "1.1.0", "@radix-ui/react-tooltip": "1.1.2", "@swc/core": "1.4.15", "@types/babel__core": "7.20.5", "@types/babel__traverse": "*", "@types/fs-extra": "11.0.1", "@types/mime-types": "2.1.4", "@types/node": "22.10.2", "@types/normalize-path": "3.0.2", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@types/webpack": "5.28.5", "@vercel/style-guide": "5.1.0", "autoprefixer": "10.4.20", "clsx": "2.1.1", "framer-motion": "12.0.0-alpha.2", "jiti": "2.4.2", "json5": "2.2.3", "module-punycode": "npm:punycode@2.3.1", "node-html-parser": "6.1.13", "postcss": "8.4.40", "prettier-plugin-tailwindcss": "0.6.6", "pretty-bytes": "6.1.1", "prism-react-renderer": "2.1.0", "react": "19.0.0", "react-dom": "19.0.0", "sharp": "0.33.3", "socket.io-client": "4.8.0", "sonner": "1.7.1", "source-map-js": "1.0.2", "spamc": "0.0.5", "stacktrace-parser": "0.1.10", "tailwind-merge": "2.2.0", "tailwindcss": "3.4.0", "tsup": "7.2.0", "tsx": "4.9.0", "typescript": "5.8.2", "use-debounce": "10.0.4", "zod": "3.24.2"}, "scripts": {"build": "next build", "caniemail:fetch": "node ./scripts/fill-caniemail-data.mjs", "clean": "rm -rf dist", "dev": "tsup-node --watch", "dev:preview": "cd ../../apps/demo && tsx ../../packages/react-email/src/cli/index.ts dev", "test": "vitest run", "test:watch": "vitest", "start": "next start"}}