export const IconWarning = (props: React.ComponentProps<'svg'>) => (
  <svg
    width="13"
    height="12"
    viewBox="0 0 13 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M10.8777 8.99999L6.87766 1.99999C6.79044 1.84609 6.66396 1.71808 6.51112 1.62902C6.35828 1.53997 6.18455 1.49304 6.00766 1.49304C5.83077 1.49304 5.65704 1.53997 5.5042 1.62902C5.35136 1.71808 5.22488 1.84609 5.13766 1.99999L1.13766 8.99999C1.0495 9.15267 1.00327 9.32594 1.00366 9.50224C1.00405 9.67855 1.05105 9.85161 1.13988 10.0039C1.22872 10.1562 1.35623 10.2823 1.50951 10.3694C1.66278 10.4565 1.83636 10.5016 2.01266 10.5H10.0127C10.1881 10.4998 10.3604 10.4535 10.5123 10.3656C10.6642 10.2778 10.7903 10.1515 10.8779 9.99955C10.9656 9.84756 11.0117 9.67518 11.0116 9.49973C11.0116 9.32428 10.9654 9.15193 10.8777 8.99999Z"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.0127 4.5V6.5"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.0127 8.5H6.01853"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

IconWarning.displayName = 'IconCircleWarning';
