import { checkRedisHealth, getQueueStats } from '@repo/email-queue';
import { log } from '@repo/observability/log';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    const [stats, redisHealthy] = await Promise.all([
      getQueueStats(),
      checkRedisHealth(),
    ]);

    return NextResponse.json({
      success: true,
      data: {
        ...stats,
        redisHealthy,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    log.error('Failed to get queue stats', { error });
    return NextResponse.json(
      { success: false, error: 'Failed to get queue statistics' },
      { status: 500 }
    );
  }
}
