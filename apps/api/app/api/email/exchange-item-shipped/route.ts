import { sendExchangeItemShippedEmail } from '@repo/email';
import { type NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

const requestSchema = z.object({
  to: z.string().email(),
  returnNumber: z.string().min(1),
  trackingNumber: z.string().min(1),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { to, returnNumber, trackingNumber } = requestSchema.parse(body);

    const result = await sendExchangeItemShippedEmail(
      to,
      returnNumber,
      trackingNumber
    );

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Email sent successfully',
      });
    }

    return NextResponse.json(
      { success: false, error: 'Failed to send email' },
      { status: 500 }
    );
  } catch (error) {
    console.error('Email API error:', error);
    return NextResponse.json(
      { success: false, error: 'Invalid request data' },
      { status: 400 }
    );
  }
}
