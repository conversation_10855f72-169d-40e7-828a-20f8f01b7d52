import { render } from '@react-email/components';
import { ReturnRequestAutoApprovedEmail } from '@repo/email';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Test rendering an email template
    const emailHtml = await render(
      ReturnRequestAutoApprovedEmail({
        returnNumber: 'TEST-12345',
        refundDays: '5-7',
      })
    );

    return new Response(emailHtml, {
      headers: {
        'Content-Type': 'text/html',
      },
    });
  } catch (error) {
    console.error('Email test error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to render email template' },
      { status: 500 }
    );
  }
}
