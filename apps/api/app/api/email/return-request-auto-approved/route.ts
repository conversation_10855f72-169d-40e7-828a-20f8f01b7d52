import { sendReturnRequestAutoApprovedEmail } from '@repo/email';
import { type NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

const requestSchema = z.object({
  to: z.string().email(),
  returnNumber: z.string().min(1),
  refundDays: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { to, returnNumber, refundDays } = requestSchema.parse(body);

    const result = await sendReturnRequestAutoApprovedEmail(
      to,
      returnNumber,
      refundDays
    );

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Email sent successfully',
      });
    }
    return NextResponse.json(
      { success: false, error: 'Failed to send email' },
      { status: 500 }
    );
  } catch (error) {
    console.error('Email API error:', error);
    return NextResponse.json(
      { success: false, error: 'Invalid request data' },
      { status: 400 }
    );
  }
}
