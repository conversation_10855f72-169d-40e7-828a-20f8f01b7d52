'use client';

import {
  Alert,
  AlertDescription,
} from '@repo/design-system/components/ui/alert';
import { Skeleton } from '@repo/design-system/components/ui/skeleton';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';

interface CmsContentProps {
  contentKey: string;
  locale?: string;
  fallback?: string;
}

interface CmsContentData {
  key: string;
  title: string;
  content: string;
}

export function CmsContent({
  contentKey,
  locale,
  fallback = '',
}: CmsContentProps) {
  const [content, setContent] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const params = useParams();

  // Simple translation for error message
  const failedToLoadText =
    params.locale === 'ja'
      ? 'コンテンツの読み込みに失敗しました'
      : 'Failed to load content';

  useEffect(() => {
    const fetchContent = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const url = new URL('/api/cms', window.location.origin);
        url.searchParams.append('key', contentKey);
        if (locale) {
          url.searchParams.append('locale', locale);
        }

        const response = await fetch(url.toString());

        if (!response.ok) {
          throw new Error(`Failed to fetch content: ${response.statusText}`);
        }

        const data: CmsContentData = await response.json();
        setContent(data.content);
      } catch (err) {
        console.error('Error fetching CMS content:', err);
        setError(failedToLoadText);
        // Use fallback content if provided
        if (fallback) {
          setContent(fallback);
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchContent();
  }, [contentKey, locale, fallback, failedToLoadText]);

  if (isLoading) {
    return (
      <div>
        <Skeleton className="h-5 w-full" />
      </div>
    );
  }

  if (error && !content) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  // If no content is available, return fallback or empty div
  if (!content) {
    return fallback ? <div>{fallback}</div> : <div />;
  }

  return (
    <div
      className="cms-content"
      // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
}
