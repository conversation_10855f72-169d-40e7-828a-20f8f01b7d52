'use client';

import { updateReturnRequest } from '@/app/[locale]/(authenticated)/return-requests/actions';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@repo/design-system/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { toast } from '@repo/design-system/components/ui/sonner';
import { Textarea } from '@repo/design-system/components/ui/textarea';
import type { Dictionary } from '@repo/internationalization';
import { useState } from 'react';
import { z } from 'zod';

const formSchema = z.object({
  status: z.enum(['pending', 'approved', 'rejected', 'completed']),
  processed: z.string(),
  adminNotes: z.string().optional(),
});

type ReturnRequestStatusFormProps = {
  id: string;
  currentStatus: string;
  processed: string;
  dictionary: Dictionary;
};

export function ReturnRequestStatusForm({
  id,
  currentStatus,
  processed,
  dictionary,
}: ReturnRequestStatusFormProps) {
  const [open, setOpen] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      status: currentStatus as
        | 'pending'
        | 'approved'
        | 'rejected'
        | 'completed',
      processed,
      adminNotes: '',
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      await updateReturnRequest(id, values);
      toast.success(dictionary.admin.return_requests.status_updated);
      setOpen(false);
    } catch (error) {
      console.error('Failed to update return request:', error);
      toast.error(dictionary.admin.return_requests.status_update_failed);
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">
          {dictionary.admin.return_requests.update_status}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {dictionary.admin.return_requests.update_status_title}
          </DialogTitle>
          <DialogDescription>
            {dictionary.admin.return_requests.update_status_description}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {dictionary.admin.return_requests.status || 'Status'}
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="pending">
                        {dictionary.admin.return_requests.status_pending}
                      </SelectItem>
                      <SelectItem value="approved">
                        {dictionary.admin.return_requests.status_approved}
                      </SelectItem>
                      <SelectItem value="rejected">
                        {dictionary.admin.return_requests.status_rejected}
                      </SelectItem>
                      <SelectItem value="completed">
                        {dictionary.admin.return_requests.status_completed}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    {dictionary.admin.return_requests.status_description}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="processed"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {dictionary.admin.return_requests.processed}
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="pending">
                        {dictionary.admin.return_requests.status_pending}
                      </SelectItem>
                      <SelectItem value="return_shipped">
                        {dictionary.admin.return_requests.status_return_shipped}
                      </SelectItem>
                      <SelectItem value="completed">
                        {dictionary.admin.return_requests.status_completed}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    {dictionary.admin.return_requests.processed_description}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="adminNotes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {dictionary.admin.return_requests.admin_notes}
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={
                        dictionary.admin.return_requests.admin_notes_placeholder
                      }
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    {dictionary.admin.return_requests.admin_notes_description}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button type="submit">
                {dictionary.admin.return_requests.save_changes}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
