'use client';

import { CheckCircle } from '@repo/design-system/components/icons';
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import type { Dictionary } from '@repo/internationalization';
import { log } from '@repo/observability/log';
import { updateReturnRequest } from '../actions';
import { BarcodeScanner } from './barcode-scanner';

interface BarcodeScannerTabProps {
  returnRequest: {
    id: string;
    processed: string;
    exchangeType: string | null;
    returnNumber: string;
    returnItems: Array<{
      id: string;
      title: string;
      sku: string | null;
      quantity: number;
      barcode: string | null;
    }>;
  };
  dictionary: Dictionary;
}

export function BarcodeScannerTab({
  returnRequest,
  dictionary,
}: BarcodeScannerTabProps) {
  const handleItemScanned = (
    itemId: string,
    scannedBarcode: string,
    quantity: number
  ) => {
    // Here you could also save the scanned status to the database
    log.info(
      `Item ${itemId} scanned with barcode: ${scannedBarcode}, quantity: ${quantity}`
    );
  };

  const handleSubmit = async (
    scannedItems: { itemId: string; scannedQuantity: number }[]
  ) => {
    log.info('All items verified:', scannedItems);

    // Determine the next processed status based on exchange type and current status
    let newProcessedStatus = 'completed';

    if (returnRequest.exchangeType === 'exchange') {
      if (returnRequest.processed === 'pending') {
        newProcessedStatus = 'exchange_shipped';
      } else if (returnRequest.processed === 'exchange_shipped') {
        newProcessedStatus = 'completed';
      }
    }

    log.info(`Updating return request status to: ${newProcessedStatus}`);

    await updateReturnRequest(returnRequest.id, {
      processed: newProcessedStatus,
    });
  };

  if (returnRequest.processed === 'completed') {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {returnRequest.exchangeType === 'exchange'
              ? dictionary.admin.scanner.common.exchange_processing
              : dictionary.admin.scanner.common.return_processing}
          </CardTitle>
          <CardDescription className="mt-2 flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            {returnRequest.exchangeType === 'exchange'
              ? dictionary.admin.scanner.common.exchange_completed
              : dictionary.admin.scanner.common.return_completed}
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  // Show status-specific information for exchanges
  if (returnRequest.exchangeType === 'exchange') {
    const getExchangeStatusInfo = () => {
      switch (returnRequest.processed) {
        case 'pending':
          return {
            title: dictionary.admin.scanner.barcode_scanner.outgoing_items,
            description: dictionary.admin.scanner.barcode_scanner.scan_outgoing
              ?.replace('{scannedCount}', '0')
              .replace(
                '{totalCount}',
                returnRequest.returnItems.length.toString()
              ),
          };
        case 'exchange_shipped':
          return {
            title: dictionary.admin.scanner.barcode_scanner.incoming_items,
            description: dictionary.admin.scanner.barcode_scanner.scan_incoming
              ?.replace('{scannedCount}', '0')
              .replace(
                '{totalCount}',
                returnRequest.returnItems.length.toString()
              ),
          };
        default:
          return {
            title: dictionary.admin.scanner.common.exchange_processing,
            description: 'Process exchange request',
          };
      }
    };

    const statusInfo = getExchangeStatusInfo();

    return (
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>{statusInfo.title}</CardTitle>
            <CardDescription>{statusInfo.description}</CardDescription>
          </CardHeader>
        </Card>
        <BarcodeScanner
          returnItems={returnRequest.returnItems}
          exchangeType={returnRequest.exchangeType}
          returnNumber={returnRequest.returnNumber}
          processed={returnRequest.processed}
          dictionary={dictionary}
          onItemScanned={handleItemScanned}
          onSubmit={handleSubmit}
        />
      </div>
    );
  }

  return (
    <BarcodeScanner
      returnItems={returnRequest.returnItems}
      exchangeType={returnRequest.exchangeType}
      returnNumber={returnRequest.returnNumber}
      processed={returnRequest.processed}
      dictionary={dictionary}
      onItemScanned={handleItemScanned}
      onSubmit={handleSubmit}
    />
  );
}
