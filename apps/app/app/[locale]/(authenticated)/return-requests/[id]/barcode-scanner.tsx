'use client';

import { But<PERSON> } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import type { Dictionary } from '@repo/internationalization';
import { Volume2, VolumeX } from 'lucide-react';
import { useState } from 'react';
import { BarcodeInput } from './components/barcode-input';
import { ExchangeStepControls } from './components/exchange-step-controls';
import { ItemsList } from './components/items-list';
import { ProgressIndicator } from './components/progress-indicator';
import { ScanResult } from './components/scan-result';
import { SubmitButton } from './components/submit-button';
import { useScanningLogic } from './hooks/use-scanning-logic';

interface ReturnItem {
  id: string;
  title: string;
  sku: string | null;
  barcode: string | null;
  quantity: number;
  currentQty?: number;
}

interface BarcodeScannerProps {
  skipReturnPackageScan?: boolean;
  returnItems: ReturnItem[];
  exchangeType: string | null;
  returnNumber: string;
  processed?: string;
  dictionary: Dictionary;
  onItemScanned?: (
    itemId: string,
    scannedBarcode: string,
    quantity: number
  ) => void;
  onSubmit?: (
    scannedItems: { itemId: string; scannedQuantity: number }[]
  ) => void;
}

export function BarcodeScanner({
  skipReturnPackageScan = false,
  returnItems,
  exchangeType,
  returnNumber,
  processed,
  dictionary,
  onItemScanned,
  onSubmit,
}: BarcodeScannerProps) {
  // Exchange step state - set initial step based on processed status
  const getInitialExchangeStep = () => {
    if (exchangeType === 'exchange' && processed) {
      if (processed === 'pending') {
        return 'outgoing';
      }
      if (processed === 'exchange_shipped') {
        return 'incoming';
      }
    }
    return 'outgoing';
  };

  const [exchangeStep, setExchangeStep] = useState<'outgoing' | 'incoming'>(
    getInitialExchangeStep()
  );
  const [audioEnabled, setAudioEnabled] = useState(true);

  // Use the scanning logic hook
  const {
    productChecklist,
    scanResult,
    errorMessage,
    returnPackageVerified,
    scanningStep,
    scannedCount,
    totalCount,
    allScanned,
    setAudioEnabled: setScanningAudioEnabled,
    handleReturnPackageVerification,
    handleCheckProduct,
    handleExchangeScanning,
    handleMarkAsScanned,
    resetChecklist,
  } = useScanningLogic({
    returnItems,
    returnNumber,
    initialScanningStep: skipReturnPackageScan ? 'items' : 'package',
    onItemScanned,
  });

  // Sync audio enabled state
  const handleAudioToggle = () => {
    setAudioEnabled(!audioEnabled);
    setScanningAudioEnabled(!audioEnabled);
  };

  // Handle scanning based on type and step
  const handleScan = (value: string) => {
    if (exchangeType === 'return' && scanningStep === 'package') {
      handleReturnPackageVerification(value);
    } else if (exchangeType === 'return' && scanningStep === 'items') {
      handleCheckProduct(value);
    } else if (exchangeType === 'exchange' && exchangeStep === 'outgoing') {
      handleExchangeScanning(value, 'outgoing');
    } else if (exchangeType === 'exchange' && exchangeStep === 'incoming') {
      handleExchangeScanning(value, 'incoming');
    } else {
      // Default behavior for null exchange type (legacy returns)
      handleCheckProduct(value);
    }
  };

  // Handle exchange step change
  const handleExchangeStepChange = (step: 'outgoing' | 'incoming') => {
    setExchangeStep(step);
    resetChecklist();
  };

  // Helper functions for UI content
  const getCardTitle = () => {
    if (exchangeType === 'exchange') {
      return exchangeStep === 'outgoing'
        ? dictionary.admin.scanner.barcode_scanner.outgoing_items
        : dictionary.admin.scanner.barcode_scanner.incoming_items;
    }
    if (exchangeType === 'return' && scanningStep === 'package') {
      return dictionary.admin.scanner.barcode_scanner.package_verification;
    }
    return dictionary.admin.scanner.barcode_scanner.item_verification;
  };

  const getCardDescription = () => {
    if (exchangeType === 'exchange') {
      if (exchangeStep === 'outgoing') {
        return (
          dictionary.admin.scanner.barcode_scanner.scan_outgoing
            .replace('{scannedCount}', scannedCount.toString())
            .replace('{totalCount}', totalCount.toString()) ||
          `Scan outgoing exchange items for shipment (${scannedCount}/${totalCount} scanned)`
        );
      }

      return (
        dictionary.admin.scanner.barcode_scanner.scan_incoming
          .replace('{scannedCount}', scannedCount.toString())
          .replace('{totalCount}', totalCount.toString()) ||
        `Scan incoming items received from customer (${scannedCount}/${totalCount} received)`
      );
    }
    if (exchangeType === 'return' && scanningStep === 'package') {
      return dictionary.admin.scanner.barcode_scanner.scan_package.replace(
        '{returnNumber}',
        returnNumber
      );
    }
    return dictionary.admin.scanner.barcode_scanner.scan_items
      .replace('{scannedCount}', scannedCount.toString())
      .replace('{totalCount}', totalCount.toString());
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>{getCardTitle()}</span>
          <Button
            variant="outline"
            size="sm"
            onClick={handleAudioToggle}
            className="flex items-center gap-2"
          >
            {audioEnabled ? (
              <Volume2 className="h-4 w-4" />
            ) : (
              <VolumeX className="h-4 w-4" />
            )}
            {audioEnabled
              ? dictionary.admin.scanner.barcode_scanner.audio_on
              : dictionary.admin.scanner.barcode_scanner.audio_off}
          </Button>
        </CardTitle>
        <CardDescription>{getCardDescription()}</CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Exchange step controls */}
        {exchangeType === 'exchange' && (
          <ExchangeStepControls
            exchangeStep={exchangeStep}
            processed={processed}
            onStepChange={handleExchangeStepChange}
          />
        )}

        {/* Progress indicator */}
        <ProgressIndicator
          exchangeType={exchangeType}
          scanningStep={scanningStep}
          returnPackageVerified={returnPackageVerified}
          scannedCount={scannedCount}
          totalCount={totalCount}
        />

        {/* Scan result and error messages */}
        <ScanResult scanResult={scanResult} errorMessage={errorMessage} />

        {/* Barcode Scanner Input */}
        <BarcodeInput
          exchangeType={exchangeType}
          exchangeStep={exchangeStep}
          scanningStep={scanningStep}
          returnNumber={returnNumber}
          onScan={handleScan}
          dictionary={dictionary}
        />

        {/* Items list */}
        <ItemsList
          items={productChecklist}
          exchangeType={exchangeType}
          exchangeStep={exchangeStep}
          scanningStep={scanningStep}
          onMarkAsScanned={handleMarkAsScanned}
        />

        {/* Submit button */}
        <SubmitButton
          exchangeType={exchangeType}
          exchangeStep={exchangeStep}
          scanningStep={scanningStep}
          returnPackageVerified={returnPackageVerified}
          allScanned={allScanned}
          productChecklist={productChecklist}
          onSubmit={(scannedItems) => {
            onSubmit?.(scannedItems);

            if (exchangeType === 'exchange' && exchangeStep === 'outgoing') {
              handleExchangeStepChange('incoming');
              return;
            }
          }}
          dictionary={dictionary}
        />
      </CardContent>
    </Card>
  );
}
