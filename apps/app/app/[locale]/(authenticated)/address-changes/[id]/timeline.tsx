'use client';

import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>le,
} from '@repo/design-system/components/ui/card';
import {
  Timeline,
  TimelineContent,
  TimelineDate,
  TimelineHeader,
  TimelineIndicator,
  TimelineItem,
  TimelineSeparator,
  TimelineTitle,
} from '@repo/design-system/components/ui/timeline';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@repo/design-system/components/ui/tooltip';
import {} from '@repo/design-system/components/ui/tooltip';
import { dateFormatter } from '@repo/design-system/lib/format';
import type { Dictionary } from '@repo/internationalization';
import { Calendar } from 'lucide-react';

interface Address {
  name?: string;
  firstName?: string;
  lastName?: string;
  address1?: string;
  address2?: string;
  city?: string;
  province?: string;
  zip?: string;
  country?: string;
  phone?: string;
  company?: string;
}

interface TimelineEvent {
  id: string;
  type: 'created' | 'updated' | 'approved' | 'rejected' | 'completed';
  title: string;
  description: string;
  timestamp: Date;
  user?: string;
  status?: string;
  changes?: string[];
}

interface TimelineProps {
  addressChange: {
    id: string;
    orderName: string;
    status: string;
    createdAt: string;
    updatedAt: string;
    adminNotes: string | null;
    originalAddress?: Address;
    newAddress?: Address;
  };
  dictionary: Dictionary;
}

// Helper function to compare addresses and generate change descriptions
function getAddressChanges(
  originalAddress?: Address,
  newAddress?: Address,
  dictionary?: Dictionary
): string[] {
  if (!originalAddress || !newAddress || !dictionary) {
    return [];
  }

  const changes: string[] = [];
  const fieldLabels: Record<keyof Address, string> = {
    name: dictionary.admin.scanner.address_timeline.name,
    firstName: dictionary.admin.scanner.address_timeline.first_name,
    lastName: dictionary.admin.scanner.address_timeline.last_name,
    address1: dictionary.admin.scanner.address_timeline.address_line_1,
    address2: dictionary.admin.scanner.address_timeline.address_line_2,
    city: dictionary.admin.scanner.address_timeline.city,
    province: dictionary.admin.scanner.address_timeline.state_province,
    zip: dictionary.admin.scanner.address_timeline.zip_postal,
    country: dictionary.admin.scanner.address_timeline.country,
    phone: dictionary.admin.scanner.address_timeline.phone,
    company: dictionary.admin.scanner.address_timeline.company,
  };

  for (const field of Object.keys(fieldLabels) as Array<keyof Address>) {
    const oldValue = originalAddress[field];
    const newValue = newAddress[field];

    if (oldValue !== newValue) {
      const label = fieldLabels[field];
      const oldDisplay =
        oldValue || `(${dictionary.admin.scanner.address_timeline.empty})`;
      const newDisplay =
        newValue || `(${dictionary.admin.scanner.address_timeline.empty})`;
      changes.push(`${label}: ${oldDisplay} → ${newDisplay}`);
    }
  }

  return changes;
}

export function AddressTimeline({ addressChange, dictionary }: TimelineProps) {
  // Generate timeline events based on address change data
  const generateTimelineEvents = (): TimelineEvent[] => {
    const events: TimelineEvent[] = [];

    // Get address changes for tooltips
    const addressChanges = getAddressChanges(
      addressChange.originalAddress,
      addressChange.newAddress,
      dictionary
    );

    // Created event
    events.push({
      id: 'created',
      type: 'created',
      title: dictionary.admin.scanner.address_timeline.address_change_requested,
      description:
        dictionary.admin.scanner.address_timeline.customer_requested_change.replace(
          '{orderName}',
          addressChange.orderName
        ),
      timestamp: new Date(addressChange.createdAt),
      user: dictionary.admin.scanner.address_timeline.customer,
      changes: addressChanges,
    });

    // Status change events (simulated based on current status)
    if (addressChange.status === 'approved') {
      events.push({
        id: 'approved',
        type: 'approved',
        title: dictionary.admin.scanner.address_timeline.request_approved,
        description:
          dictionary.admin.scanner.address_timeline.approved_by_admin,
        timestamp: new Date(addressChange.updatedAt),
        user: dictionary.admin.scanner.address_timeline.admin,
        status: 'approved',
      });
    } else if (addressChange.status === 'rejected') {
      events.push({
        id: 'rejected',
        type: 'rejected',
        title: dictionary.admin.scanner.address_timeline.request_rejected,
        description:
          addressChange.adminNotes ||
          dictionary.admin.scanner.address_timeline.rejected_by_admin,
        timestamp: new Date(addressChange.updatedAt),
        user: dictionary.admin.scanner.address_timeline.admin,
        status: 'rejected',
      });
    } else if (addressChange.status === 'completed') {
      events.push({
        id: 'approved',
        type: 'approved',
        title: dictionary.admin.scanner.address_timeline.request_approved,
        description:
          dictionary.admin.scanner.address_timeline.approved_by_admin,
        timestamp: new Date(addressChange.updatedAt),
        user: dictionary.admin.scanner.address_timeline.admin,
        status: 'approved',
      });
      events.push({
        id: 'completed',
        type: 'completed',
        title: dictionary.admin.scanner.address_timeline.address_updated,
        description:
          dictionary.admin.scanner.address_timeline.successfully_updated,
        timestamp: new Date(addressChange.updatedAt),
        user: dictionary.admin.scanner.address_timeline.system,
        status: 'completed',
      });
    }

    return events.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  };

  const events = generateTimelineEvents();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          {dictionary.admin.scanner.address_timeline.request_timeline}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Timeline defaultValue={3}>
          {events.map((item, index) => {
            const hasChanges = item.changes && item.changes.length > 0;

            const timelineItem = (
              <TimelineItem
                key={item.id}
                step={index}
                className="group-data-[orientation=vertical]/timeline:sm:ms-32"
              >
                <TimelineHeader>
                  <TimelineSeparator />
                  <TimelineDate className="group-data-[orientation=vertical]/timeline:sm:-left-32 group-data-[orientation=vertical]/timeline:sm:absolute group-data-[orientation=vertical]/timeline:sm:w-20 group-data-[orientation=vertical]/timeline:sm:text-right">
                    {dateFormatter(item.timestamp, 'MMM d, yyyy h:mm a')}
                  </TimelineDate>
                  <TimelineTitle className="sm:-mt-0.5">
                    {item.title}
                  </TimelineTitle>
                  <TimelineIndicator />
                </TimelineHeader>
                <TimelineContent>{item.description}</TimelineContent>
              </TimelineItem>
            );

            // If there are changes, wrap with tooltip
            if (hasChanges) {
              return (
                <Tooltip key={item.id}>
                  <TooltipTrigger asChild>{timelineItem}</TooltipTrigger>
                  <TooltipContent side="right" className="max-w-sm">
                    <div className="space-y-1">
                      <div className="font-semibold text-sm">
                        {
                          dictionary.admin.scanner.address_timeline
                            .address_changes
                        }
                      </div>
                      {item.changes?.map((change, changeIndex) => (
                        <div key={changeIndex} className="text-xs">
                          {change}
                        </div>
                      ))}
                    </div>
                  </TooltipContent>
                </Tooltip>
              );
            }

            return timelineItem;
          })}
        </Timeline>
      </CardContent>
    </Card>
  );
}
