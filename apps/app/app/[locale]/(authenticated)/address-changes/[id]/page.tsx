import { Header } from '@/app/[locale]/(authenticated)/components/header';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { dateFormatter } from '@repo/design-system/lib/format';
import { getDictionary } from '@repo/internationalization';
import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { getAddressChangeRequest } from '../actions';
import { AddressTimeline } from './timeline';

type PageProps = {
  readonly params: Promise<{
    id: string;
    locale: string;
  }>;
};

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const { id, locale } = await params;
  const dictionary = await getDictionary(locale);
  const addressChange = await getAddressChangeRequest(id);

  return {
    title: `${dictionary.admin.title} - ${dictionary.admin.address_changes.title}`,
    description: `${dictionary.admin.address_changes.detail.address_change_information} ${addressChange.orderName}`,
  };
}

export default async function AddressChangeDetailPage({
  params,
}: PageProps): Promise<ReactElement> {
  const { id, locale } = await params;
  const dictionary = await getDictionary(locale);
  const addressChange = await getAddressChangeRequest(id);

  // Parse the JSON address objects
  const originalAddress = addressChange.originalAddress as Record<
    string,
    string
  >;
  const newAddress = addressChange.newAddress as Record<string, string>;

  return (
    <>
      <Header
        pages={[dictionary.admin.address_changes.title]}
        page={addressChange.orderName}
      />

      <main className="flex-1 space-y-4 p-4 pt-6 lg:p-8">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="font-bold text-xl tracking-tight">
            {dictionary.admin.address_changes.title} {addressChange.orderName}
          </h2>
          {/* <AddressChangeStatusForm
            id={addressChange.id}
            currentStatus={addressChange.status}
          /> */}
        </div>

        <div className="grid gap-4 lg:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>
                {
                  dictionary.admin.address_changes.detail
                    .address_change_information
                }
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {dictionary.admin.address_changes.detail.order}
                </span>
                <span>{addressChange.orderName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {dictionary.admin.address_changes.detail.customer_email}
                </span>
                <span>{addressChange.email}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {dictionary.admin.address_changes.status}
                </span>
                <span className="capitalize">{addressChange.status}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {dictionary.admin.address_changes.created_at}
                </span>
                <span>
                  {dateFormatter(
                    new Date(addressChange.createdAt),
                    'MMM d, yyyy h:mm a'
                  )}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {dictionary.admin.address_changes.detail.last_updated}
                </span>
                <span>
                  {dateFormatter(
                    new Date(addressChange.updatedAt),
                    'MMM d, yyyy h:mm a'
                  )}
                </span>
              </div>
              {addressChange.adminNotes && (
                <div className="mt-4">
                  <span className="text-muted-foreground">
                    {dictionary.admin.return_requests.admin_notes}
                  </span>
                  <p className="mt-1 whitespace-pre-wrap">
                    {addressChange.adminNotes}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          <div className="grid gap-4">
            <Card>
              <CardHeader>
                <CardTitle>
                  {dictionary.admin.address_changes.original_address}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {originalAddress.name && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {dictionary.address.first_name}
                    </span>
                    <span>{originalAddress.name}</span>
                  </div>
                )}
                {originalAddress.address1 && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {dictionary.address.line1}
                    </span>
                    <span>{originalAddress.address1}</span>
                  </div>
                )}
                {originalAddress.address2 && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {dictionary.address.line2}
                    </span>
                    <span>{originalAddress.address2}</span>
                  </div>
                )}
                {originalAddress.city && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {dictionary.address.city}
                    </span>
                    <span>{originalAddress.city}</span>
                  </div>
                )}
                {originalAddress.province && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {dictionary.address.state}
                    </span>
                    <span>{originalAddress.province}</span>
                  </div>
                )}
                {originalAddress.zip && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {dictionary.address.postal}
                    </span>
                    <span>{originalAddress.zip}</span>
                  </div>
                )}
                {originalAddress.country && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {dictionary.address.country}
                    </span>
                    <span>{originalAddress.country}</span>
                  </div>
                )}
                {originalAddress.phone && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {dictionary.address.phone_optional}
                    </span>
                    <span>{originalAddress.phone}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>
                  {dictionary.admin.address_changes.new_address}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {newAddress.name && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {dictionary.address.first_name}
                    </span>
                    <span>{newAddress.name}</span>
                  </div>
                )}
                {newAddress.address1 && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {dictionary.address.line1}
                    </span>
                    <span>{newAddress.address1}</span>
                  </div>
                )}
                {newAddress.address2 && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {dictionary.address.line2}
                    </span>
                    <span>{newAddress.address2}</span>
                  </div>
                )}
                {newAddress.city && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {dictionary.address.city}
                    </span>
                    <span>{newAddress.city}</span>
                  </div>
                )}
                {newAddress.province && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {dictionary.address.state}
                    </span>
                    <span>{newAddress.province}</span>
                  </div>
                )}
                {newAddress.zip && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {dictionary.address.postal}
                    </span>
                    <span>{newAddress.zip}</span>
                  </div>
                )}
                {newAddress.country && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {dictionary.address.country}
                    </span>
                    <span>{newAddress.country}</span>
                  </div>
                )}
                {newAddress.phone && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {dictionary.address.phone_optional}
                    </span>
                    <span>{newAddress.phone}</span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Timeline Section */}
        <AddressTimeline
          addressChange={{
            ...addressChange,
            originalAddress,
            newAddress,
          }}
          dictionary={dictionary}
        />
      </main>
    </>
  );
}
