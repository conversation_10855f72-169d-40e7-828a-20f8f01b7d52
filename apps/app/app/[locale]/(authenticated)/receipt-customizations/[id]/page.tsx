import { Header } from '@/app/[locale]/(authenticated)/components/header';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { formatDateTime } from '@repo/design-system/lib/format';
import { getDictionary } from '@repo/internationalization';
import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { getReceiptCustomization } from '../actions';

type PageProps = {
  readonly params: Promise<{
    id: string;
    locale: string;
  }>;
};

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const { id, locale } = await params;
  const dictionary = await getDictionary(locale);
  const receiptCustomization = await getReceiptCustomization(id);

  return {
    title: `${dictionary.admin.title} - ${dictionary.admin.receipt_customizations.title}`,
    description: `${dictionary.admin.receipt_customizations.detail.customization_information} ${receiptCustomization.orderName}`,
  };
}

export default async function ReceiptCustomizationDetailPage({
  params,
}: PageProps): Promise<ReactElement> {
  const { id, locale } = await params;
  const dictionary = await getDictionary(locale);
  const receiptCustomization = await getReceiptCustomization(id);

  return (
    <>
      <Header
        pages={[dictionary.admin.receipt_customizations.title]}
        page={receiptCustomization.orderName}
      />

      <main className="flex-1 space-y-4 p-4 pt-6 lg:p-8">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="font-bold text-xl tracking-tight">
            {dictionary.admin.receipt_customizations.title}{' '}
            {receiptCustomization.orderName}
          </h2>
        </div>

        <div className="grid gap-4 lg:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>
                {
                  dictionary.admin.receipt_customizations.detail
                    .customization_information
                }
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {dictionary.admin.receipt_customizations.detail.order}
                </span>
                <span>{receiptCustomization.orderName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {
                    dictionary.admin.receipt_customizations.detail
                      .customer_email
                  }
                </span>
                <span>{receiptCustomization.email}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {
                    dictionary.admin.receipt_customizations.detail
                      .recipient_name
                  }
                </span>
                <span>{receiptCustomization.recipientName || '-'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {dictionary.admin.receipt_customizations.detail.note}
                </span>
                <span>{receiptCustomization.note || '-'}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>
                {
                  dictionary.admin.receipt_customizations.detail
                    .download_information
                }
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {
                    dictionary.admin.receipt_customizations.detail
                      .download_count
                  }
                </span>
                <span>{receiptCustomization.downloadCount}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {
                    dictionary.admin.receipt_customizations.detail
                      .last_downloaded
                  }
                </span>
                <span>
                  {receiptCustomization.lastDownloaded
                    ? formatDateTime(receiptCustomization.lastDownloaded)
                    : dictionary.admin.receipt_customizations.detail.never}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {dictionary.admin.receipt_customizations.created_at}
                </span>
                <span>{formatDateTime(receiptCustomization.createdAt)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {dictionary.admin.receipt_customizations.detail.last_updated}
                </span>
                <span>{formatDateTime(receiptCustomization.updatedAt)}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </>
  );
}
