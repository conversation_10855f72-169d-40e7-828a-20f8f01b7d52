import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from '@repo/design-system/components/ui/card';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger,
} from '@repo/design-system/components/ui/tabs';
import { getDictionary } from '@repo/internationalization';
import type { Metadata } from 'next';
import { title } from 'radash';
import type { ReactElement } from 'react';
import { Header } from '../../../components/header';
import { getUser } from '../actions';

type PageProps = {
  readonly params: Promise<{
    id: string;
    locale: string;
  }>;
};

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const { id, locale } = await params;
  const dictionary = await getDictionary(locale);
  const user = await getUser(id);

  return {
    title: `${dictionary.admin.title} - ${dictionary.admin.users.title}`,
    description: `${dictionary.admin.users.detail.user_information} ${user.firstName} ${user.lastName}`,
  };
}

export default async function UserDetailPage({
  params,
}: PageProps): Promise<ReactElement> {
  const { id, locale } = await params;
  const dictionary = await getDictionary(locale);
  const user = await getUser(id);

  return (
    <>
      <Header pages={[dictionary.admin.users.title]} page={user.id} />

      <main className="flex-1 space-y-4 px-4 lg:px-8">
        <div className="flex items-center justify-between space-y-2">
          {user.firstName || user.lastName ? (
            <h2 className="font-bold text-xl tracking-tight">{`${user.firstName} ${user.lastName}`}</h2>
          ) : (
            <h2 className="font-bold text-xl tracking-tight">
              {dictionary.admin.users.detail.user_information}
            </h2>
          )}
        </div>
        <Tabs defaultValue="summary">
          <div className="flex items-center justify-between">
            <TabsList>
              <TabsTrigger value="summary">
                {dictionary.admin.users.detail.summary}
              </TabsTrigger>
            </TabsList>
            <div className="flex items-center gap-2" />
          </div>
          <TabsContent value="summary" className="mt-6">
            <div className="grid gap-4 lg:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>
                    {dictionary.admin.users.detail.user_information}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {dictionary.admin.users.detail.name}
                    </span>
                    <span>{`${user.firstName ?? ''} ${user.lastName ?? ''}`}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {dictionary.admin.users.detail.email}
                    </span>
                    <span>{user.email}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {dictionary.address.phone_optional}
                    </span>
                    <span>{user.phone}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Date of Birth</span>
                    <span>
                      {user.dob
                        ? new Date(user.dob).toLocaleDateString()
                        : 'N/A'}
                    </span>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>
                    {dictionary.admin.users.detail.account_information}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      {dictionary.admin.users.detail.role}
                    </span>
                    <span className="capitalize">{title(user.role)}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </main>
    </>
  );
}
