import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { Skeleton } from '@repo/design-system/components/ui/skeleton';
import { Suspense } from 'react';
import { EmailQueueControls } from './components/email-queue-controls';
import { EmailQueueJobs } from './components/email-queue-jobs';
import { EmailQueueStats } from './components/email-queue-stats';

export default function EmailQueuePage() {
  return (
    <div className="container mx-auto space-y-6 py-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="font-bold text-3xl tracking-tight">Email Queue</h1>
          <p className="text-muted-foreground">
            Monitor and manage email processing queue
          </p>
        </div>
      </div>

      <div className="grid gap-6">
        {/* Queue Statistics */}
        <Suspense fallback={<QueueStatsSkeleton />}>
          <EmailQueueStats />
        </Suspense>

        {/* Queue Controls */}
        <Card>
          <CardHeader>
            <CardTitle>Queue Controls</CardTitle>
            <CardDescription>
              Manage queue operations and health
            </CardDescription>
          </CardHeader>
          <CardContent>
            <EmailQueueControls />
          </CardContent>
        </Card>

        {/* Recent Jobs */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Jobs</CardTitle>
            <CardDescription>
              View recent email jobs and their status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Suspense fallback={<JobsSkeleton />}>
              <EmailQueueJobs />
            </Suspense>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

function QueueStatsSkeleton() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {Array.from({ length: 4 }).map((_, i) => (
        <Card key={i}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-4" />
          </CardHeader>
          <CardContent>
            <Skeleton className="mb-2 h-8 w-16" />
            <Skeleton className="h-3 w-24" />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

function JobsSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 5 }).map((_, i) => (
        <div key={i} className="flex items-center space-x-4">
          <Skeleton className="h-12 w-12 rounded-full" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-[250px]" />
            <Skeleton className="h-4 w-[200px]" />
          </div>
        </div>
      ))}
    </div>
  );
}
