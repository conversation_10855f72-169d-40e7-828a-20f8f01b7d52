'use client';

import { Badge } from '@repo/design-system/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { Activity, CheckCircle, Clock, Loader, XCircle } from 'lucide-react';
import {} from 'react';
import useSWR from 'swr';

// interface QueueStats {
//   waiting: number;
//   active: number;
//   completed: number;
//   failed: number;
//   total: number;
//   redisHealthy: boolean;
//   timestamp: string;
// }

export function EmailQueueStats() {
  const {
    data: response,
    error,
    isLoading: loading,
  } = useSWR('/api/email-queue/stats');

  if (loading) {
    return <div>Loading queue statistics...</div>;
  }

  if (error) {
    return (
      <Card className="border-destructive">
        <CardContent>
          <CardDescription className="text-destructive">
            {error?.message?.toString() ?? 'Failed to fetch queue stats'}
          </CardDescription>
        </CardContent>
      </Card>
    );
  }

  if (!response.data) {
    return null;
  }

  const stats = response.data;

  const statCards = [
    {
      title: 'Waiting',
      value: stats.waiting,
      icon: Clock,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
    },
    {
      title: 'Active',
      value: stats.active,
      icon: Loader,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Completed',
      value: stats.completed,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Failed',
      value: stats.failed,
      icon: XCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
    },
  ];

  return (
    <div className="space-y-4">
      {/* Redis Health Status */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="font-medium text-sm">System Health</CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <Badge variant={stats.redisHealthy ? 'default' : 'destructive'}>
              Redis: {stats.redisHealthy ? 'Healthy' : 'Unhealthy'}
            </Badge>
            <span className="text-muted-foreground text-xs">
              Last updated: {new Date(stats.timestamp).toLocaleTimeString()}
            </span>
          </div>
        </CardContent>
      </Card>

      {/* Queue Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {statCards.map((stat) => {
          const Icon = stat.icon;
          return (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="font-medium text-sm">
                  {stat.title}
                </CardTitle>
                <div className={`rounded-full p-2 ${stat.bgColor}`}>
                  <Icon className={`h-4 w-4 ${stat.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="font-bold text-2xl">{stat.value}</div>
                <p className="text-muted-foreground text-xs">
                  {stat.title.toLowerCase()} jobs
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Total Jobs */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="font-medium text-sm">Total Jobs</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="font-bold text-2xl">{stats.total}</div>
          <p className="text-muted-foreground text-xs">All jobs in the queue</p>
        </CardContent>
      </Card>
    </div>
  );
}
