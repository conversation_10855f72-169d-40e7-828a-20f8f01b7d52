import { getDictionary, locales } from '@repo/internationalization';
import { Header } from '../../../components/header';
import { getCmsContent } from '../actions';
import { CmsContentForm } from '../components/cms-content-form';

interface CmsContentEditPageProps {
  params: Promise<{
    id: string;
    locale: string;
  }>;
}

export default async function CmsContentEditPage({
  params,
}: CmsContentEditPageProps) {
  const { id, locale } = await params;
  const dictionary = await getDictionary(locale);
  const content = await getCmsContent(id);
  const isNew = id === 'new';
  const title = isNew
    ? `${dictionary.admin.cms.title} - 新規作成`
    : `${dictionary.admin.cms.title} - 編集`;

  return (
    <>
      <Header
        pages={[dictionary.admin.cms.title]}
        page={content?.title ?? title}
      />

      <main className="flex-1 space-y-4 px-4 lg:px-8">
        <div className="flex items-center justify-between space-y-2">
          {content?.title ? (
            <h2 className="font-bold text-xl tracking-tight">
              {content.title}
            </h2>
          ) : (
            <h2 className="font-bold text-xl tracking-tight">
              {dictionary.admin.cms.title}
            </h2>
          )}
        </div>

        <div className="flex flex-1 flex-col gap-4">
          <div className="min-h-[100vh] flex-1 rounded-xl md:min-h-min">
            <CmsContentForm
              content={content}
              locales={locales}
              dictionary={dictionary}
            />
          </div>
        </div>
      </main>
    </>
  );
}
