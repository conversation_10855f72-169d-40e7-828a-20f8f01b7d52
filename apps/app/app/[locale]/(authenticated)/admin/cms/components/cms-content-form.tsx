'use client';

import { <PERSON><PERSON> } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormLabel,
  FormMessage,
  GridFormItem,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import { Switch } from '@repo/design-system/components/ui/switch';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@repo/design-system/components/ui/tabs';
import { Textarea } from '@repo/design-system/components/ui/textarea';
import { useToast } from '@repo/design-system/components/ui/use-toast';
import type { Dictionary } from '@repo/internationalization';
import { ArrowLeft, Save } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { z } from 'zod';
import {
  type ContentFormData,
  type SerializedCmsContent,
  createOrUpdateContent,
} from '../actions';

// Define the form schema with Zod
const formSchema = z.object({
  id: z.string(),
  key: z.string().min(1, 'Content key is required'),
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  isPublished: z.boolean(),
  content: z.record(z.string(), z.string()),
});

// Define the form type
type FormValues = z.infer<typeof formSchema>;

interface CmsContentFormProps {
  content: SerializedCmsContent | null;
  locales: readonly string[];
  dictionary: Dictionary;
}

export function CmsContentForm({
  content,
  locales,
  dictionary,
}: CmsContentFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  const isNew = !content;
  const [_, setActiveLocale] = useState(locales[0]);

  // Initialize form with react-hook-form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      id: content?.id || '',
      key: content?.key || '',
      title: content?.title || '',
      description: content?.description || '',
      isPublished: content?.isPublished ?? true,
      content:
        (content?.content as Record<string, string>) ||
        Object.fromEntries(locales.map((locale) => [locale, ''])),
    },
  });

  // Handle content changes for the active locale
  const handleContentChange = (locale: string, value: string) => {
    const currentContent = form.getValues('content');
    form.setValue('content', {
      ...currentContent,
      [locale]: value,
    });
  };

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    try {
      await createOrUpdateContent(values as ContentFormData);

      toast({
        title: isNew
          ? dictionary.admin.cms.content_created
          : dictionary.admin.cms.content_updated,
        description: `${isNew ? dictionary.admin.cms.success_created : dictionary.admin.cms.success_updated} ${values.title}`,
      });

      router.push('/admin/cms');
      router.refresh();
    } catch (error) {
      console.error('Error saving content:', error);
      toast({
        title: dictionary.admin.cms.error,
        description: `${isNew ? dictionary.admin.cms.failed_create : dictionary.admin.cms.failed_update}`,
        variant: 'destructive',
      });
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <Card>
          <CardHeader>
            <CardTitle>
              {isNew
                ? dictionary.admin.cms.add_new_content
                : dictionary.admin.cms.edit_content}
            </CardTitle>
            <CardDescription>
              {isNew
                ? dictionary.admin.cms.create_new_dynamic
                : dictionary.admin.cms.edit_existing}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="key"
                render={({ field }) => (
                  <GridFormItem className="space-y-2">
                    <FormLabel>{dictionary.admin.cms.content_key}</FormLabel>
                    <FormControl>
                      <Input
                        placeholder={
                          dictionary.admin.cms.content_key_placeholder
                        }
                        {...field}
                      />
                    </FormControl>
                    <FormDescription className="text-xs">
                      {dictionary.admin.cms.content_key_description}
                    </FormDescription>
                    <FormMessage />
                  </GridFormItem>
                )}
              />

              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <GridFormItem className="space-y-2">
                    <FormLabel>{dictionary.admin.cms.title}</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Delivery Note" {...field} />
                    </FormControl>
                    <FormMessage />
                  </GridFormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <GridFormItem className="space-y-2">
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Brief description of this content"
                      rows={2}
                      {...field}
                      value={field.value || ''}
                    />
                  </FormControl>
                  <FormMessage />
                </GridFormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isPublished"
              render={({ field }) => (
                <GridFormItem className="space-y-2">
                  <div className="flex items-center justify-between">
                    <FormLabel>Published</FormLabel>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </div>
                  <FormDescription className="text-xs">
                    Only published content is visible on the portal
                  </FormDescription>
                  <FormMessage />
                </GridFormItem>
              )}
            />

            <div className="space-y-2">
              <FormLabel>Content by Language</FormLabel>
              <Tabs defaultValue={locales[0]} onValueChange={setActiveLocale}>
                <TabsList className="mb-4">
                  {locales.map((locale) => (
                    <TabsTrigger key={locale} value={locale}>
                      {locale.toUpperCase()}
                    </TabsTrigger>
                  ))}
                </TabsList>

                {locales.map((locale) => (
                  <TabsContent key={locale} value={locale}>
                    <Textarea
                      placeholder={`Content in ${locale}`}
                      value={form.getValues().content[locale] || ''}
                      onChange={(e) =>
                        handleContentChange(locale, e.target.value)
                      }
                      rows={8}
                    />
                  </TabsContent>
                ))}
              </Tabs>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" type="button" asChild>
              <Link href="/admin/cms">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Link>
            </Button>
            <Button type="submit">
              <Save className="mr-2 h-4 w-4" />
              {isNew ? 'Create' : 'Update'}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
}
