'use client';

import type { SerializedCmsContent } from '@/types';
import type { Dictionary } from '@repo/internationalization';
import { CmsContentTable } from './components/cms-content-table';
import { columns } from './components/columns';

export function CmsPageClient({
  content,
  dictionary,
}: {
  content: NonNullable<SerializedCmsContent>[];
  dictionary: Dictionary;
}) {
  return (
    <CmsContentTable columns={columns(dictionary)} initialData={content} />
  );
}
