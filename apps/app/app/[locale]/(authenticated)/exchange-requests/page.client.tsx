'use client';

import { But<PERSON> } from '@repo/design-system/components/ui/button';
import { Card, CardContent } from '@repo/design-system/components/ui/card';
import {
  Tabs,
  TabsContent,
  Ta<PERSON>List,
  TabsTrigger,
} from '@repo/design-system/components/ui/tabs';
import type { Dictionary } from '@repo/internationalization';
import Link from 'next/link';
import {
  generateYamatoCsv,
  type getExchangeRequests,
} from '../return-requests/actions';
import { columns } from '../return-requests/components/return-request-column';
import { ReturnRequestTable } from '../return-requests/components/return-request-table';
import { UploadYamatoCsv } from '../return-requests/components/upload-yamato-csv';

export const ExchangeRequestsPageClient = ({
  dictionary,
  exchangeRequests,
}: {
  dictionary: Dictionary;
  exchangeRequests: Awaited<ReturnType<typeof getExchangeRequests>>;
}) => {
  return (
    <Tabs defaultValue="exchange">
      <TabsList>
        <TabsTrigger value="exchange">
          {dictionary.admin.exchange_requests.exchange_requests_tab}
        </TabsTrigger>
        <TabsTrigger value="yamato">
          {dictionary.admin.exchange_requests.yamato_processing_tab}
        </TabsTrigger>
      </TabsList>
      <TabsContent value="exchange">
        <ReturnRequestTable
          columns={columns(dictionary)}
          initialData={exchangeRequests}
          type="exchange"
        />
      </TabsContent>
      <TabsContent value="yamato">
        <Card className="mt-4 flex flex-col items-start gap-2">
          <CardContent className="space-y-4 ">
            <div>
              <p className="mb-1 text-sm">
                {dictionary.admin.exchange_requests.download_csv_step}
              </p>
              <div>
                <Button
                  onClick={async () => {
                    const result = await generateYamatoCsv();

                    if (result.error) {
                      console.error('Error generating CSV:', result.error);
                      return;
                    }

                    if (result.success && result.data) {
                      // Create blob and download
                      const binaryString = atob(result.data);
                      const bytes = new Uint8Array(binaryString.length);
                      for (let i = 0; i < binaryString.length; i++) {
                        bytes[i] = binaryString.charCodeAt(i);
                      }

                      const blob = new Blob([bytes], { type: result.mimeType });
                      const url = URL.createObjectURL(blob);

                      const a = document.createElement('a');
                      a.href = url;
                      a.download = result.filename;
                      document.body.appendChild(a);
                      a.click();
                      document.body.removeChild(a);
                      URL.revokeObjectURL(url);
                    }
                  }}
                >
                  {dictionary.admin.exchange_requests.download_yamato_csv}
                </Button>
              </div>
            </div>
            <div>
              <p className="mb-1 text-sm">
                {dictionary.admin.exchange_requests.go_to_yamato_step}
              </p>
              <Link href="https://bmypage.kuronekoyamato.co.jp/bmypage/">
                <Button>
                  {dictionary.admin.exchange_requests.go_to_yamato}
                </Button>
              </Link>
            </div>
            <div>
              <p className="mb-1 text-sm">
                {dictionary.admin.exchange_requests.upload_csv_step}
              </p>
              <UploadYamatoCsv dictionary={dictionary} />
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
};
