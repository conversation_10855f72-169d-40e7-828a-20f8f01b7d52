{"name": "app", "private": true, "scripts": {"dev": "next dev -p 3000 --turbopack", "build": "next build", "start": "next start", "analyze": "ANALYZE=true pnpm build", "test": "NODE_ENV=test vitest run", "clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@repo/analytics": "workspace:*", "@repo/auth": "workspace:*", "@repo/database": "workspace:*", "@repo/design-system": "workspace:*", "@repo/email-queue": "workspace:*", "@repo/feature-flags": "workspace:*", "@repo/next-config": "workspace:*", "@repo/notifications": "workspace:*", "@repo/observability": "workspace:*", "@repo/security": "workspace:*", "@repo/seo": "workspace:*", "@sentry/nextjs": "^9.13.0", "@shopify/admin-api-client": "^1.0.8", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-table": "^8.21.3", "export-to-csv": "^1.4.0", "fuse.js": "^7.1.0", "import-in-the-middle": "^1.13.1", "kuroshiro": "^1.2.0", "kuroshiro-analyzer-kuromoji": "^1.1.0", "lucide-react": "^0.488.0", "next": "15.3.0", "next-themes": "^0.4.6", "papaparse": "^5.5.3", "radash": "^12.1.0", "react": "19.1.0", "react-dom": "19.1.0", "require-in-the-middle": "^7.5.2", "swr": "^2.3.3", "zod": "^3.24.3"}, "devDependencies": {"@repo/testing": "workspace:*", "@repo/typescript-config": "workspace:*", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.3.0", "@types/node": "22.14.1", "@types/papaparse": "^5.3.16", "@types/react": "19.1.2", "@types/react-dom": "19.1.2", "jsdom": "^26.1.0", "tailwindcss": "^4.1.4", "typescript": "^5.8.3", "vitest": "^3.1.1"}}